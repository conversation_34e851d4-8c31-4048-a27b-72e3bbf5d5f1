version: '{branch}-{build}'
image:
  - 'Visual Studio 2022'
branches:
  only:
    - 1.19
    - GreatHorn
    - Executions
environment:
  JAVA_HOME: C:\Program Files\Java\jdk17
cache:
  - C:/Users/<USER>/.gradle
  - ./.gradle
build_script:
  - cmd: |
      gradlew cleanBuild idea remapSpigotJar --no-daemon -i --stacktrace --refresh-dependencies
      gradlew build --no-daemon -i --stacktrace
      rem Due to a MixinGradle bug, we need to build it twice
      gradlew build collect --no-daemon -i --stacktrace
test: off
artifacts:
  - path: ./build/libs/*.jar
    name: .