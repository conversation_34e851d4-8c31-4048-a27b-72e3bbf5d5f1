buildscript {
    repositories {
        maven { url = 'https://maven.minecraftforge.net' }
        maven { url = 'https://repo.spongepowered.org/repository/maven-public' }
        mavenCentral()
        maven { url = 'https://maven.izzel.io/releases' }
        // maven { url = 'https://maven.parchmentmc.org' }
    }
    dependencies {
        classpath group: 'net.minecraftforge.gradle', name: 'ForgeGradle', version: "${forge_gradle_version}", changing: true
        // classpath 'org.parchmentmc:librarian:1.+'
        classpath 'org.spongepowered:mixingradle:0.7.2-SNAPSHOT'
        classpath "io.izzel.arclight:arclight-gradle-plugin:$agpVersion"
    }
}

apply plugin: 'net.minecraftforge.gradle'
// apply plugin: 'org.parchmentmc.librarian.forgegradle'
apply plugin: 'org.spongepowered.mixin'
apply plugin: 'java'
apply plugin: 'idea'
apply plugin: 'io.izzel.arclight'

archivesBaseName = 'luminara-common-' + minecraftVersion

arclight {
    mcVersion = minecraftVersion
    forgeVersion = project.ext.forgeVersion
    bukkitVersion = 'v1_20_R1'
    wipeVersion = true
    reobfVersion = true
    accessTransformer = project.file('bukkit.at')
    // packageName = 'spigot'
}

configurations {
    embed
}

java.toolchain.languageVersion = JavaLanguageVersion.of(17)

minecraft {
    //mappings channel: 'parchment', version: "2021.08.22-$minecraftVersion"
    mappings channel: 'official', version: minecraftVersion
    // copyIdeResources = true
    accessTransformer = project.file('src/main/resources/META-INF/accesstransformer.cfg')
}

repositories {
    maven { url = 'https://repo.spongepowered.org/repository/maven-public' }
    maven { url = 'https://oss.sonatype.org/content/repositories/snapshots/' }
    maven { url = 'https://hub.spigotmc.org/nexus/content/repositories/snapshots/' }
    maven { url = 'https://files.minecraftforge.net/maven/' }
    maven { url = 'https://maven.izzel.io/releases' }
    maven { url = 'https://jitpack.io/' }

    mavenCentral()
}

dependencies {
    minecraft "net.minecraftforge:forge:$minecraftVersion-$forgeVersion"
    implementation "org.spigotmc:spigot-api:$minecraftVersion-R0.1-SNAPSHOT@jar"
    implementation 'org.jetbrains:annotations:23.0.0'

    implementation "org.spongepowered:mixin:$mixinVersion"
    annotationProcessor 'org.spongepowered:mixin:0.8.3:processor'

    implementation 'com.github.ArclightTeam:mixin-tools:1.0.0'
    annotationProcessor 'com.github.ArclightTeam:mixin-tools:1.0.0'

    implementation 'jline:jline:2.12.1'
    implementation 'net.md-5:SpecialSource:1.11.0'
    implementation 'org.apache.logging.log4j:log4j-jul:2.15.0'
    implementation 'net.md-5:bungeecord-chat:1.16-R0.4'
    implementation 'mysql:mysql-connector-java:5.1.49'
    implementation 'org.yaml:snakeyaml:2.0'
    implementation "io.izzel:tools:$toolsVersion"
    implementation "io.izzel.arclight:arclight-api:$apiVersion"
    implementation project(':i18n-config')

    // Adventure dependencies - core only to avoid conflicts
    implementation("net.kyori:adventure-api:4.17.0") {
        exclude group: 'org.slf4j'
        exclude group: 'org.checkerframework'
    }
    implementation("net.kyori:adventure-text-serializer-gson:4.17.0") {
        exclude group: 'org.slf4j'
        exclude group: 'org.checkerframework'
        exclude group: 'com.google.code.gson'
    }
    implementation("net.kyori:adventure-text-serializer-legacy:4.17.0") {
        exclude group: 'org.slf4j'
        exclude group: 'org.checkerframework'
    }
    implementation("net.kyori:adventure-text-serializer-plain:4.17.0") {
        exclude group: 'org.slf4j'
        exclude group: 'org.checkerframework'
    }
    implementation("net.kyori:adventure-text-minimessage:4.17.0") {
        exclude group: 'org.slf4j'
        exclude group: 'org.checkerframework'
    }


    embed 'net.md-5:bungeecord-chat:1.16-R0.4@jar'
    embed "org.spigotmc:spigot-api:$minecraftVersion-R0.1-SNAPSHOT@jar"
    embed("net.kyori:adventure-api:4.17.0") {
        exclude group: 'org.slf4j'
        exclude group: 'org.checkerframework'
    }
    embed("net.kyori:adventure-text-serializer-gson:4.17.0") {
        exclude group: 'org.slf4j'
        exclude group: 'org.checkerframework'
        exclude group: 'com.google.code.gson'
    }
    embed("net.kyori:adventure-text-serializer-legacy:4.17.0") {
        exclude group: 'org.slf4j'
        exclude group: 'org.checkerframework'
    }
    embed("net.kyori:adventure-text-serializer-plain:4.17.0") {
        exclude group: 'org.slf4j'
        exclude group: 'org.checkerframework'
    }
    embed("net.kyori:adventure-text-minimessage:4.17.0") {
        exclude group: 'org.slf4j'
        exclude group: 'org.checkerframework'
    }

    embed("net.kyori:adventure-text-logger-slf4j:4.17.0") {
        exclude group: 'org.slf4j'
        exclude group: 'org.checkerframework'
    }
}

jar {
    manifest.attributes 'MixinConnector': 'io.izzel.arclight.common.mod.ArclightConnector'
    manifest.attributes 'Implementation-Title': 'Luminara'
    manifest.attributes 'Implementation-Version': "luminara-$minecraftVersion-${project.version}-$gitHash"
    manifest.attributes 'Implementation-Vendor': 'Luminara Team'
    manifest.attributes 'Implementation-Timestamp': new Date().format("yyyy-MM-dd HH:mm:ss")
    from { configurations.embed.collect { it.isDirectory() ? it : zipTree(it) } } {
        exclude "META-INF/MANIFEST.MF"
        exclude "META-INF/*.SF"
        exclude "META-INF/*.DSA"
        exclude "META-INF/*.RSA"
        exclude "LICENSE.txt"
        exclude "META-INF/services/**"
        exclude "org/apache/commons/lang/enum/**"
    }
    duplicatesStrategy = DuplicatesStrategy.EXCLUDE
}

tasks.register('srgJar', Jar) {
    from(tasks.jar.outputs.files.collect { it.isDirectory() ? it : zipTree(it) }) {
        include 'io/izzel/**'
        exclude 'io/izzel/arclight/common/mixin/**'
    }
    archiveClassifier.set('srg')
}

tasks.register('spigotJar', Jar) {
    with tasks.srgJar
    doLast {
        def task = tasks.create('renameSpigotJar', net.minecraftforge.gradle.userdev.tasks.RenameJarInPlace)
        task.input.set(archiveFile.get().asFile)
        task.hasLog = false
        task.mappings.set(project.file('build/arclight_cache/tmp_srg/bukkit_srg.srg'))
        task.args.add('--reverse')
        task.apply()
    }
    archiveClassifier.set('spigot')
}

afterEvaluate {
    tasks.reobfJar.configure {
        it.extraMappings.from(project.files('extra_mapping.tsrg'))
    }
}

processResources {
    filesMatching("**/mods.toml") {
        expand 'version': "$minecraftVersion-${project.version}-$gitHash"
    }
}

remapSpigotJar {
    includes.add('net/minecraft/world/level/block/ChestBlock$DoubleInventory')
}

mixin {
    add sourceSets.main, 'mixins.arclight.refmap.json'
}

compileJava {
    options.compilerArgs << '-XDignore.symbol.file' << '-XDenableSunApiLintControl'
    options.encoding = 'UTF-8'
}
