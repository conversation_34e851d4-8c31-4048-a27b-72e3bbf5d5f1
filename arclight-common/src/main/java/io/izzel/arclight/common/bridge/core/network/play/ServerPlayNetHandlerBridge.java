package io.izzel.arclight.common.bridge.core.network.play;

import org.bukkit.Location;
import org.bukkit.event.player.PlayerTeleportEvent;

public interface ServerPlayNetHandlerBridge {

    void bridge$pushTeleportCause(PlayerTeleportEvent.TeleportCause cause);

    void bridge$disconnect(String str);

    void bridge$teleport(Location dest);

    boolean bridge$processedDisconnect();

    boolean bridge$isDisconnected();

    int bridge$getLatency();
}
