# Arclight 1.20
public net.minecraft.world.entity.AreaEffectCloud f_19701_ # potion
public net.minecraft.world.entity.item.FallingBlockEntity f_31947_ # cancelDrop
public net.minecraft.server.dedicated.DedicatedServerProperties$WorldDimensionData <init>(Lcom/google/gson/JsonObject;Ljava/lang/String;)V
public net.minecraft.world.entity.projectile.Arrow f_36855_ # potion
public net.minecraft.world.entity.Display$TextDisplay f_268476_ # DATA_LINE_WIDTH_ID
public net.minecraft.world.entity.Display$TextDisplay f_268494_ # DATA_BACKGROUND_COLOR_ID
public net.minecraft.world.level.block.entity.SculkCatalystBlockEntity$CatalystListener m_280309_(Lnet/minecraft/server/level/ServerLevel;Lnet/minecraft/core/BlockPos;Lnet/minecraft/world/level/block/state/BlockState;Lnet/minecraft/util/RandomSource;)V # bloom
public net.minecraft.world.level.block.SculkSpreader$ChargeCursor f_222288_ # pos
public net.minecraft.world.level.block.SculkSpreader$ChargeCursor f_222289_ # charge
# Arclight 1.19.4
public net.minecraft.world.item.DebugStickItem m_150802_(Lnet/minecraft/world/entity/player/Player;Lnet/minecraft/world/level/block/state/BlockState;Lnet/minecraft/world/level/LevelAccessor;Lnet/minecraft/core/BlockPos;ZLnet/minecraft/world/item/ItemStack;)Z # handleInteraction
public net.minecraft.world.level.block.ComposterBlock m_269590_(Lnet/minecraft/world/entity/Entity;Lnet/minecraft/world/level/block/state/BlockState;Lnet/minecraft/world/level/LevelAccessor;Lnet/minecraft/core/BlockPos;)Lnet/minecraft/world/level/block/state/BlockState; # empty
public net.minecraft.world.entity.player.Player m_6107_()Z # isImmobile
public net.minecraft.server.packs.repository.Pack f_244124_ # resources
public net.minecraft.world.flag.FeatureFlag f_243952_ # universe
public net.minecraft.world.flag.FeatureFlagRegistry f_244560_ # names
public-f net.minecraft.server.players.PlayerList f_11193_ # maxPlayers
public net.minecraft.world.level.block.ChiseledBookShelfBlock m_261279_(Lnet/minecraft/world/phys/Vec2;)I # getHitSlot
# Arclight 1.19.3
public net.minecraft.server.MinecraftServer$TimeProfiler <init>(JI)V # TimeProfiler
public net.minecraft.world.level.block.entity.SkullBlockEntity f_262250_ # noteBlockSound
public net.minecraft.world.entity.player.Player f_36081_ # enchantmentSeed
public net.minecraft.world.level.StructureManager f_220460_ # level
public net.minecraft.world.level.levelgen.structure.templatesystem.StructurePlaceSettings f_74369_ # palette
# Arclight 1.18.2
public-f net.minecraft.server.ReloadableServerResources f_206847_ # commands
public net.minecraft.world.level.chunk.ChunkGenerator f_212255_
public net.minecraft.world.level.chunk.ChunkGenerator f_207956_
public net.minecraft.world.level.chunk.ChunkGenerator f_207955_
# Arclight 1.18
public net.minecraft.server.level.PlayerRespawnLogic m_183928_(Lnet/minecraft/server/level/ServerLevel;II)Lnet/minecraft/core/BlockPos;
public net.minecraft.world.entity.npc.Villager m_35524_()V # releaseAllPois
public net.minecraft.world.entity.Entity f_19861_ # onGround
public net.minecraft.world.level.block.ChestBlock$2$1
public net.minecraft.world.entity.animal.horse.SkeletonHorse f_30892_ # trapTime
public net.minecraft.world.entity.monster.Vindicator f_34071_ # Here's Johnny!
# Arclight 1.17
public net.minecraft.world.entity.Entity f_19851_
public net.minecraft.world.entity.raid.Raider$HoldGroundAttackGoal
public net.minecraft.world.entity.monster.SpellcasterIllager$SpellcasterUseSpellGoal
public net.minecraft.world.entity.monster.SpellcasterIllager$IllagerSpell
public-f net.minecraft.network.protocol.handshake.ClientIntentionPacket f_134721_ # hostName
public net.minecraft.server.network.ServerGamePacketListenerImpl$EntityInteraction
public net.minecraft.server.MinecraftServer$TimeProfiler
public net.minecraft.world.level.chunk.ChunkGenerator f_62140_ # strongholdSeed
public net.minecraft.world.level.levelgen.NoiseBasedChunkGenerator f_64318_ # settings
public net.minecraft.world.entity.monster.piglin.PiglinAi m_149965_(Lnet/minecraft/world/item/ItemStack;)Z # isLovedItem
public net.minecraft.world.level.chunk.storage.EntityStorage f_156538_ # level
public net.minecraft.world.level.levelgen.structure.templatesystem.StructureTemplate f_74482_ # palettes
public net.minecraft.world.level.levelgen.structure.templatesystem.StructureTemplate f_74483_ # entityInfoList
public net.minecraft.world.level.levelgen.structure.templatesystem.StructureManager f_74326_ # structureRepository
public net.minecraft.world.level.block.entity.LecternBlockEntity$1
public net.minecraft.world.level.chunk.storage.EntityStorage f_182485_ # entityDeserializerQueue
public net.minecraft.world.level.entity.PersistentEntitySectionManager f_157493_ # permanentStorage
public net.minecraft.world.level.entity.PersistentEntitySectionManager$ChunkLoadStatus
# Misc
public net.minecraft.server.PlayerAdvancements f_135964_
public net.minecraft.server.level.PlayerRespawnLogic m_8264_(Lnet/minecraft/server/level/ServerLevel;IIZ)Lnet/minecraft/core/BlockPos;
public net.minecraft.world.level.block.state.BlockBehaviour$BlockStateBase f_60599_
public net.minecraft.world.level.dimension.end.EndDragonFight f_64073_
public net.minecraft.world.level.dimension.end.EndDragonFight f_64072_
public net.minecraft.world.level.dimension.end.EndDragonFight f_64061_
public net.minecraft.world.level.dimension.end.EndDragonFight f_64060_
public net.minecraft.world.level.dimension.end.EndDragonFight f_64070_
public net.minecraft.world.level.dimension.end.EndDragonFight m_64087_(Lnet/minecraft/world/level/dimension/end/DragonRespawnAnimation;)V
public net.minecraft.world.level.dimension.end.EndDragonFight m_64105_()Lnet/minecraft/world/level/block/state/pattern/BlockPattern$BlockPatternMatch;
public net.minecraft.world.level.dimension.end.EndDragonFight m_64093_(Z)V
public net.minecraft.world.entity.raid.Raids f_37951_
public net.minecraft.world.level.block.entity.BeehiveBlockEntity$BeeData
public net.minecraft.world.level.block.entity.BeehiveBlockEntity$BeeData f_58782_
public net.minecraft.world.entity.projectile.Fireball m_37018_()Lnet/minecraft/world/item/ItemStack;
# public net.minecraft.network.protocol.game.ServerboundClientInformationPacket f_133864_
# public net.minecraft.network.protocol.game.ServerboundClientInformationPacket f_133863_
public net.minecraft.world.SimpleContainer f_19147_
public net.minecraft.server.dedicated.Settings f_139798_
public net.minecraft.world.entity.vehicle.MinecartCommandBlock f_38503_
public-f net.minecraft.world.item.trading.MerchantOffer f_45311_
public-f net.minecraft.world.item.trading.MerchantOffer f_45310_
public net.minecraft.world.item.trading.MerchantOffer f_45313_
public net.minecraft.world.item.trading.MerchantOffer f_45312_
public net.minecraft.world.item.trading.MerchantOffer f_45315_
public-f net.minecraft.world.item.trading.MerchantOffer f_45314_
public net.minecraft.world.item.trading.MerchantOffer f_45319_
public net.minecraft.world.item.trading.MerchantOffer f_45318_
public net.minecraft.world.food.FoodData f_38697_
public net.minecraft.world.food.FoodData f_38698_
public net.minecraft.world.food.FoodData f_38696_
public net.minecraft.world.level.BaseSpawner f_45442_
public net.minecraft.world.level.BaseSpawner f_45453_
public net.minecraft.world.level.BaseSpawner f_45443_
public net.minecraft.world.level.BaseSpawner f_45444_
public net.minecraft.world.level.BaseSpawner f_45447_
public net.minecraft.world.level.BaseSpawner f_45448_
public net.minecraft.world.level.BaseSpawner f_45449_
public net.minecraft.world.level.BaseSpawner f_45451_
public net.minecraft.world.level.BaseSpawner f_45452_
public net.minecraft.world.inventory.DispenserMenu f_39431_
public net.minecraft.network.chat.Style <init>(Lnet/minecraft/network/chat/TextColor;Ljava/lang/Boolean;Ljava/lang/Boolean;Ljava/lang/Boolean;Ljava/lang/Boolean;Ljava/lang/Boolean;Lnet/minecraft/network/chat/ClickEvent;Lnet/minecraft/network/chat/HoverEvent;Ljava/lang/String;Lnet/minecraft/resources/ResourceLocation;)V
public net.minecraft.world.level.saveddata.maps.MapItemSavedData f_77896_
public net.minecraft.world.item.ItemCooldowns f_41515_
public net.minecraft.world.item.ItemCooldowns f_41516_
public net.minecraft.world.entity.monster.piglin.AbstractPiglin f_34649_
public net.minecraft.world.entity.monster.piglin.AbstractPiglin m_34665_()Z
public-f net.minecraft.world.level.LevelSettings f_46902_
public-f net.minecraft.world.level.LevelSettings f_46904_
public-f net.minecraft.world.item.crafting.RecipeManager f_44007_
public net.minecraft.network.protocol.game.ServerboundMovePlayerPacket f_134118_
public net.minecraft.network.protocol.game.ServerboundMovePlayerPacket f_134125_
public net.minecraft.network.protocol.game.ServerboundMovePlayerPacket f_134124_
public net.minecraft.network.protocol.game.ServerboundMovePlayerPacket f_134122_
public net.minecraft.network.protocol.game.ServerboundMovePlayerPacket f_134121_
public net.minecraft.network.protocol.game.ServerboundMovePlayerPacket f_134120_
public net.minecraft.network.protocol.game.ServerboundMovePlayerPacket f_134119_
public net.minecraft.server.commands.TeleportCommand$LookAt
public-f net.minecraft.server.level.TicketType f_9452_
public net.minecraft.util.datafix.fixes.ItemIdFix f_15937_
public net.minecraft.world.level.block.entity.BrewingStandBlockEntity f_58979_
public net.minecraft.world.level.block.entity.BrewingStandBlockEntity f_58976_
public net.minecraft.world.entity.projectile.ThrownPotion m_37553_()Z
public net.minecraft.world.entity.projectile.FishingHook$FishHookState
public net.minecraft.world.entity.animal.MushroomCow m_28928_(Lnet/minecraft/world/entity/animal/MushroomCow$MushroomType;)V
public net.minecraft.world.entity.AreaEffectCloud f_19692_
public net.minecraft.world.entity.AreaEffectCloud f_19693_
public net.minecraft.world.entity.AreaEffectCloud f_19691_
public net.minecraft.world.entity.AreaEffectCloud f_19689_
public net.minecraft.world.entity.AreaEffectCloud f_19688_
public-f net.minecraft.world.entity.AreaEffectCloud f_19685_
public net.minecraft.world.entity.item.ItemEntity f_31986_
public net.minecraft.world.entity.item.ItemEntity f_31985_
public net.minecraft.network.protocol.game.ClientboundTabListPacket f_133480_
public net.minecraft.network.protocol.game.ClientboundTabListPacket f_133481_
public net.minecraft.world.dimension.DimensionType <init>(ILjava/lang/String;Ljava/lang/String;Ljava/util/function/BiFunction;ZLnet/minecraft/world/biome/IBiomeMagnifier;)V
public net.minecraft.world.level.block.entity.BarrelBlockEntity m_58606_(Lnet/minecraft/world/level/block/state/BlockState;Z)V
public net.minecraft.world.level.block.entity.BarrelBlockEntity m_58600_(Lnet/minecraft/world/level/block/state/BlockState;Lnet/minecraft/sounds/SoundEvent;)V
public net.minecraft.server.Main m_129674_(Lnet/minecraft/world/level/storage/LevelStorageSource$LevelStorageAccess;Lcom/mojang/datafixers/DataFixer;ZLjava/util/function/BooleanSupplier;Lcom/google/common/collect/ImmutableSet;)V
public net.minecraft.world.level.block.entity.AbstractFurnaceBlockEntity f_58318_
public net.minecraft.world.level.block.entity.AbstractFurnaceBlockEntity f_58319_
public net.minecraft.world.level.block.entity.AbstractFurnaceBlockEntity f_58316_
public net.minecraft.world.entity.animal.AbstractFish m_142392_()Z
public-f net.minecraft.world.inventory.AbstractContainerMenu f_38839_
public-f net.minecraft.world.inventory.AbstractContainerMenu f_38841_
public net.minecraft.server.players.StoredUserEntry m_11373_()Ljava/lang/Object;
public net.minecraft.world.entity.projectile.ThrownTrident f_37555_
public net.minecraft.world.entity.Mob f_21355_
public net.minecraft.world.entity.Mob f_21353_
public net.minecraft.world.entity.Mob f_21348_
public net.minecraft.world.entity.Mob f_21347_
public-f net.minecraft.world.entity.Mob f_21346_
public-f net.minecraft.world.entity.Mob f_21345_
public net.minecraft.world.entity.Mob f_21356_
public net.minecraft.world.item.crafting.Ingredient$ItemValue
public net.minecraft.world.item.crafting.Ingredient$ItemValue <init>(Lnet/minecraft/world/item/ItemStack;)V
public net.minecraft.world.entity.player.Abilities f_35940_
public net.minecraft.world.entity.player.Abilities f_35939_
public net.minecraft.commands.synchronization.ArgumentTypes$Entry
public net.minecraft.world.inventory.CraftingContainer f_39323_
public net.minecraft.world.entity.animal.Pig f_29459_
public net.minecraft.network.chat.TextColor f_131258_
public net.minecraft.world.entity.monster.Guardian f_32806_
public net.minecraft.world.entity.monster.Zombie f_34266_
public net.minecraft.world.entity.monster.Zombie f_34261_
public net.minecraft.world.entity.monster.Zombie m_34278_(I)V
public net.minecraft.server.network.ServerLoginPacketListenerImpl$State
public net.minecraft.world.item.crafting.Ingredient$Value
public net.minecraft.world.level.block.entity.RandomizableContainerBlockEntity f_59605_
public net.minecraft.world.level.block.entity.RandomizableContainerBlockEntity f_59606_
public net.minecraft.world.level.storage.PrimaryLevelData f_78443_
public net.minecraft.world.inventory.MerchantContainer f_40000_
public net.minecraft.world.level.chunk.DataLayer m_62557_(II)V
public net.minecraft.world.entity.decoration.ItemFrame f_31755_
public net.minecraft.world.entity.decoration.ItemFrame f_31754_
public net.minecraft.world.entity.decoration.ItemFrame m_6022_(Lnet/minecraft/core/Direction;)V
public net.minecraft.server.ServerAdvancementManager f_136022_
public net.minecraft.server.ServerAdvancementManager f_136023_
public net.minecraft.world.CompoundContainer f_18910_
public net.minecraft.world.CompoundContainer f_18911_
public net.minecraft.world.entity.projectile.AbstractArrow f_36703_
public net.minecraft.world.entity.projectile.AbstractArrow f_36699_
public net.minecraft.world.entity.projectile.AbstractArrow f_36697_
public net.minecraft.world.entity.monster.MagmaCube m_7839_(IZ)V
public net.minecraft.world.level.block.entity.BeaconBlockEntity f_58653_
public net.minecraft.world.level.block.entity.BeaconBlockEntity f_58654_
public net.minecraft.world.level.block.entity.BeaconBlockEntity f_58655_
public net.minecraft.world.level.block.entity.BeaconBlockEntity f_58650_
public net.minecraft.world.level.block.entity.BeaconBlockEntity f_58652_
public net.minecraft.server.level.ServerPlayer f_8920_
public net.minecraft.server.level.ServerPlayer f_8921_
public net.minecraft.server.level.ServerPlayer f_8927_
public net.minecraft.server.level.ServerPlayer m_9217_()V
public net.minecraft.server.level.ServerPlayer m_9209_(Lnet/minecraft/server/level/ServerLevel;)V
public net.minecraft.world.item.StandingAndWallBlockItem f_43246_
public net.minecraft.world.entity.projectile.ThrowableItemProjectile m_37454_()Lnet/minecraft/world/item/ItemStack;
public net.minecraft.world.phys.shapes.IndirectMerger <init>(Lit/unimi/dsi/fastutil/doubles/DoubleList;Lit/unimi/dsi/fastutil/doubles/DoubleList;ZZ)V
public net.minecraft.world.entity.animal.Bee f_27698_
public net.minecraft.world.entity.animal.Bee f_27711_
public net.minecraft.world.entity.animal.Bee m_27925_(Z)V
public net.minecraft.world.entity.animal.Bee m_27919_(Z)V
public net.minecraft.server.level.ServerChunkCache f_8328_
public net.minecraft.server.level.ServerChunkCache f_8335_
public net.minecraft.server.level.ServerChunkCache f_8336_
public net.minecraft.world.entity.projectile.EyeOfEnder f_36954_
public net.minecraft.world.entity.projectile.EyeOfEnder f_36951_
public net.minecraft.world.entity.projectile.EyeOfEnder f_36950_
public net.minecraft.world.entity.projectile.EyeOfEnder f_36953_
public net.minecraft.world.entity.projectile.EyeOfEnder f_36952_
public net.minecraft.network.Connection f_129469_
public net.minecraft.network.Connection f_129468_
public net.minecraft.network.protocol.game.ServerboundResourcePackPacket f_134406_
public net.minecraft.world.level.block.SoundType f_56737_
public net.minecraft.world.level.block.SoundType f_56733_
public net.minecraft.world.level.block.entity.TheEndGatewayBlockEntity f_59926_
public net.minecraft.world.level.block.entity.TheEndGatewayBlockEntity f_59929_
public net.minecraft.world.level.block.entity.TheEndGatewayBlockEntity f_59928_
public net.minecraft.world.level.block.entity.ShulkerBoxBlockEntity f_59646_
public net.minecraft.world.level.block.ShulkerBoxBlock f_56185_
public net.minecraft.world.entity.vehicle.MinecartFurnace f_38548_
public net.minecraft.world.entity.monster.Pillager f_33259_
public net.minecraft.world.entity.animal.horse.AbstractHorse f_30520_
public net.minecraft.world.entity.animal.horse.AbstractHorse m_30625_()V
public net.minecraft.world.scores.Objective f_83304_
public net.minecraft.world.level.block.DropperBlock m_5824_(Lnet/minecraft/server/level/ServerLevel;Lnet/minecraft/core/BlockPos;)V
public net.minecraft.world.entity.monster.SpellcasterIllager m_33737_()Lnet/minecraft/world/entity/monster/SpellcasterIllager$IllagerSpell;
public net.minecraft.world.level.Level f_46423_
public-f net.minecraft.world.level.Level f_46421_
public net.minecraft.world.level.Level f_46442_
public net.minecraft.nbt.TagParser m_129368_(Ljava/lang/String;)Lnet/minecraft/nbt/Tag;
public net.minecraft.nbt.TagParser m_129375_()Lnet/minecraft/nbt/Tag;
private-f net.minecraft.world.item.ItemStack f_41589_
public net.minecraft.server.level.ChunkMap$TrackedEntity
public net.minecraft.server.level.ChunkMap$TrackedEntity f_140475_
public net.minecraft.util.datafix.fixes.BlockStateData m_14942_(ILjava/lang/String;[Ljava/lang/String;)V
public net.minecraft.world.entity.animal.Animal f_27555_
public net.minecraft.world.entity.animal.Animal f_27554_
public net.minecraft.world.level.block.ComposterBlock m_52002_(Lnet/minecraft/world/level/block/state/BlockState;Lnet/minecraft/world/level/LevelAccessor;Lnet/minecraft/core/BlockPos;)Lnet/minecraft/world/level/block/state/BlockState;
public net.minecraft.world.entity.projectile.FireworkRocketEntity f_37021_
public net.minecraft.world.entity.projectile.FireworkRocketEntity f_37023_
public net.minecraft.world.entity.projectile.FireworkRocketEntity f_37019_
public net.minecraft.server.level.DistanceManager$PlayerTicketTracker
public net.minecraftforge.registries.NamespacedDefaultedWrapper
public net.minecraft.world.item.ItemCooldowns$CooldownInstance
public net.minecraft.world.item.ItemCooldowns$CooldownInstance f_41534_
public net.minecraft.server.level.ServerLevel f_8549_
public net.minecraft.server.level.DistanceManager$ChunkTicketTracker
public net.minecraft.world.entity.animal.Fox f_28439_
public net.minecraft.world.entity.animal.Fox f_28440_
public net.minecraft.world.entity.animal.Fox m_28464_(Lnet/minecraft/world/entity/animal/Fox$Type;)V
public net.minecraft.world.entity.animal.Fox m_28626_(Z)V
public net.minecraft.world.level.GameRules$IntegerValue m_7377_(Ljava/lang/String;)V
public net.minecraft.world.level.block.entity.SkullBlockEntity f_59756_
public net.minecraft.world.level.block.entity.SkullBlockEntity f_59757_
public net.minecraft.world.entity.animal.Rabbit m_8099_()V
public net.minecraft.world.entity.animal.horse.Horse m_30699_(Lnet/minecraft/world/entity/animal/horse/Variant;Lnet/minecraft/world/entity/animal/horse/Markings;)V
public net.minecraft.world.entity.monster.Strider f_33857_
public net.minecraft.util.datafix.fixes.ItemSpawnEggFix f_16031_
public net.minecraft.world.entity.raid.Raid f_37672_
public net.minecraft.world.entity.raid.Raid f_37673_
public net.minecraft.world.entity.raid.Raid f_37686_
public net.minecraft.world.entity.raid.Raid f_37678_
public net.minecraft.world.entity.raid.Raid f_37679_
public net.minecraft.world.level.GameRules$Value m_7377_(Ljava/lang/String;)V
public net.minecraft.world.level.GameRules$Value m_46368_(Lnet/minecraft/server/MinecraftServer;)V
public net.minecraft.world.level.chunk.LevelChunk f_62775_
public net.minecraft.world.level.chunk.LevelChunk f_62776_
public net.minecraft.world.level.chunk.LevelChunk f_62777_
public net.minecraft.world.level.chunk.LevelChunk f_62779_
public net.minecraft.world.entity.item.FallingBlockEntity f_31939_
public net.minecraft.world.level.block.DispenserBlock f_52661_
public net.minecraft.world.level.block.DispenserBlock m_5824_(Lnet/minecraft/server/level/ServerLevel;Lnet/minecraft/core/BlockPos;)V
public net.minecraft.server.level.DistanceManager$FixedPlayerDistanceChunkTracker
public net.minecraft.world.entity.animal.Bee$BeePollinateGoal
public net.minecraft.world.entity.animal.Bee$BeePollinateGoal m_28087_()V
public net.minecraft.world.level.chunk.ChunkBiomeContainer f_62111_
public net.minecraft.server.MinecraftServer f_129725_
public net.minecraft.server.MinecraftServer f_129746_
public net.minecraft.server.MinecraftServer f_129745_
public net.minecraft.server.MinecraftServer f_129756_
private-f net.minecraft.server.MinecraftServer f_129755_
public-f net.minecraft.server.MinecraftServer f_129744_
public net.minecraft.server.MinecraftServer f_129738_
protected-f net.minecraft.server.MinecraftServer f_129749_
public net.minecraft.server.MinecraftServer f_129759_
public net.minecraft.server.MinecraftServer f_129740_
public net.minecraft.server.MinecraftServer m_129940_(Lnet/minecraft/server/level/progress/ChunkProgressListener;)V
public net.minecraft.world.level.chunk.storage.RegionFileStorage f_63699_
public net.minecraft.world.entity.decoration.HangingEntity f_31698_
public net.minecraft.world.entity.decoration.HangingEntity m_6022_(Lnet/minecraft/core/Direction;)V
public net.minecraft.world.level.Explosion f_46016_
public net.minecraft.world.entity.LivingEntity f_20948_
public-f net.minecraft.world.entity.LivingEntity f_20926_
public net.minecraft.world.entity.LivingEntity f_20949_
public-f net.minecraft.world.entity.LivingEntity f_20944_
public net.minecraft.world.entity.LivingEntity f_20945_
public net.minecraft.world.entity.LivingEntity f_20888_
public net.minecraft.world.entity.LivingEntity f_20898_
public net.minecraft.world.entity.LivingEntity f_20940_
public net.minecraft.world.entity.LivingEntity f_20950_
public net.minecraft.world.entity.LivingEntity f_20961_
public net.minecraft.world.entity.LivingEntity m_21315_()V
public net.minecraft.world.entity.player.Player f_36110_
public net.minecraft.world.entity.player.Player m_6101_()I
public net.minecraft.world.entity.player.Player m_36364_(Lnet/minecraft/nbt/CompoundTag;)V
public net.minecraft.world.entity.player.Player m_36362_(Lnet/minecraft/nbt/CompoundTag;)V
public net.minecraft.world.entity.player.Player m_6915_()V
public net.minecraft.server.level.Ticket f_9422_
public net.minecraft.server.level.Ticket m_9429_(J)V
public net.minecraft.server.level.Ticket <init>(Lnet/minecraft/server/level/TicketType;ILjava/lang/Object;)V
public net.minecraft.server.level.Ticket <init>(Lnet/minecraft/world/server/TicketType;ILjava/lang/Object;J)V
public net.minecraft.world.level.block.entity.BannerBlockEntity f_58474_
public net.minecraft.world.level.block.entity.BannerBlockEntity f_58475_
public net.minecraft.world.LockCode f_19103_
public net.minecraft.world.item.crafting.Ingredient f_43903_
public net.minecraft.world.item.crafting.Ingredient <init>(Ljava/util/stream/Stream;)V
public net.minecraft.world.item.crafting.Ingredient m_43948_()V
public net.minecraft.advancements.AdvancementList f_139326_
public net.minecraft.world.level.GameRules$BooleanValue m_7377_(Ljava/lang/String;)V
public net.minecraft.world.entity.boss.enderdragon.EnderDragon f_31089_
public net.minecraft.world.level.storage.DimensionDataStorage f_78144_
public net.minecraft.world.entity.LightningBolt f_20862_
public net.minecraft.world.inventory.Slot f_40217_
public net.minecraft.world.level.block.JukeboxBlock m_54260_(Lnet/minecraft/world/level/Level;Lnet/minecraft/core/BlockPos;)V
public net.minecraft.world.entity.boss.wither.WitherBoss f_31430_
public net.minecraft.world.entity.item.PrimedTnt f_32072_
public net.minecraft.server.level.ChunkMap f_140139_
public net.minecraft.server.level.ChunkMap f_140136_
public net.minecraft.server.level.ChunkMap f_140129_
public net.minecraft.server.level.ChunkMap f_140130_
public net.minecraft.server.level.ChunkMap f_140150_
public net.minecraft.server.level.ChunkMap f_140145_
public net.minecraft.server.level.ChunkMap f_140144_
public net.minecraft.server.level.ChunkMap f_140133_
public net.minecraft.server.level.ChunkMap m_140258_(Lnet/minecraft/world/level/chunk/ChunkAccess;)Z
public net.minecraft.world.level.block.entity.StructureBlockEntity f_59820_
public net.minecraft.world.level.block.entity.StructureBlockEntity f_59822_
public net.minecraft.world.level.block.entity.StructureBlockEntity f_59813_
public net.minecraft.world.level.block.entity.StructureBlockEntity f_59824_
public net.minecraft.world.level.block.entity.StructureBlockEntity f_59823_
public net.minecraft.world.level.block.entity.StructureBlockEntity f_59815_
public net.minecraft.world.level.block.entity.StructureBlockEntity f_59814_
public net.minecraft.world.level.block.entity.StructureBlockEntity f_59825_
public net.minecraft.world.level.block.entity.StructureBlockEntity f_59817_
public net.minecraft.world.level.block.entity.StructureBlockEntity f_59816_
public net.minecraft.world.level.block.entity.StructureBlockEntity f_59819_
public net.minecraft.world.level.block.entity.StructureBlockEntity f_59818_
public net.minecraft.network.protocol.game.ServerboundCustomPayloadPacket f_133981_
public net.minecraft.network.protocol.game.ServerboundCustomPayloadPacket f_133980_
public net.minecraft.network.protocol.handshake.ClientIntentionPacket f_134721_
public net.minecraft.network.protocol.handshake.ClientIntentionPacket f_134722_
public-f net.minecraft.world.entity.ai.attributes.RangedAttribute f_22308_
public-f net.minecraft.server.ServerResources f_136146_
public net.minecraft.world.entity.projectile.SpectralArrow f_37409_
public net.minecraft.world.entity.monster.Creeper f_32271_
public net.minecraft.world.entity.monster.Creeper f_32272_
public net.minecraft.world.entity.monster.Creeper m_32315_()V
public net.minecraft.world.level.storage.LevelStorageSource$LevelStorageAccess f_78271_
public net.minecraft.world.inventory.CraftingMenu f_39350_
public net.minecraft.server.level.ServerBossEvent f_8298_
public net.minecraft.server.level.ChunkHolder f_140006_
public net.minecraft.server.level.ChunkHolder f_140016_
public net.minecraft.server.level.DistanceManager f_140761_
public net.minecraft.world.entity.monster.ZombieVillager f_34359_
public net.minecraft.world.entity.monster.ZombieVillager f_34365_
public net.minecraft.world.entity.monster.ZombieVillager f_34360_
public net.minecraft.world.entity.monster.ZombieVillager m_34383_(Ljava/util/UUID;I)V
public net.minecraft.world.level.block.entity.BlockEntity f_58857_
public net.minecraft.world.entity.decoration.ArmorStand f_31525_
public net.minecraft.world.entity.decoration.ArmorStand f_31526_
public net.minecraft.world.entity.decoration.ArmorStand f_31543_
public net.minecraft.world.entity.decoration.ArmorStand f_31542_
public net.minecraft.world.entity.decoration.ArmorStand f_31545_
public net.minecraft.world.entity.decoration.ArmorStand f_31544_
public net.minecraft.world.entity.decoration.ArmorStand f_31541_
public net.minecraft.world.entity.decoration.ArmorStand m_31681_(Z)V
public net.minecraft.world.entity.decoration.ArmorStand m_31678_(Z)V
public net.minecraft.world.entity.decoration.ArmorStand m_31675_(Z)V
public net.minecraft.world.entity.decoration.ArmorStand m_31603_(Z)V
public net.minecraft.world.phys.shapes.DiscreteCubeMerger <init>(II)V
public net.minecraft.world.entity.projectile.FishingHook f_37101_
public net.minecraft.world.entity.projectile.FishingHook f_37095_
public net.minecraft.world.entity.projectile.FishingHook f_37094_
public net.minecraft.world.entity.monster.Shulker f_33393_
public net.minecraft.world.level.block.entity.BedBlockEntity f_58724_
public net.minecraft.stats.RecipeBook f_12680_
public net.minecraft.world.entity.monster.Drowned f_32341_
public net.minecraft.world.entity.monster.Drowned f_32340_
public net.minecraft.world.level.block.entity.BaseContainerBlockEntity f_58621_
public net.minecraft.world.level.block.entity.BaseContainerBlockEntity f_58622_
public net.minecraft.server.players.PlayerList f_11196_
public net.minecraft.server.players.PlayerList f_11204_
public net.minecraft.server.players.PlayerList m_11273_(Lnet/minecraft/server/ServerScoreboard;Lnet/minecraft/server/level/ServerPlayer;)V
public net.minecraft.world.level.block.entity.SignBlockEntity f_59720_
public net.minecraft.world.level.block.entity.SignBlockEntity f_59721_
public net.minecraft.world.entity.monster.Slime m_7839_(IZ)V
public net.minecraft.world.level.block.ComposterBlock$EmptyContainer
public net.minecraft.world.entity.ExperienceOrb f_20770_
public net.minecraft.world.entity.monster.piglin.Piglin f_34679_
public net.minecraft.world.entity.monster.hoglin.Hoglin f_34485_
public net.minecraft.world.entity.monster.hoglin.Hoglin f_34484_
public net.minecraft.world.entity.monster.hoglin.Hoglin m_34557_()Z
public net.minecraft.world.entity.Entity f_19839_
public net.minecraft.world.entity.Entity f_19823_
public net.minecraft.world.entity.Entity f_19798_
public net.minecraft.world.entity.Entity f_19831_
public net.minecraft.world.entity.Entity m_20078_()Ljava/lang/String;
public net.minecraft.world.entity.Entity m_20291_(I)Z
public net.minecraft.world.entity.Entity m_20115_(IZ)V
public net.minecraft.world.entity.Entity m_6101_()I
public-f net.minecraft.server.dedicated.DedicatedServer f_139604_
public net.minecraft.server.dedicated.DedicatedServer f_139602_
public net.minecraft.network.protocol.game.ClientboundBlockUpdatePacket f_131732_
public net.minecraft.world.entity.projectile.Arrow f_36852_
public net.minecraft.world.entity.projectile.Arrow m_36882_(I)V
public net.minecraft.server.players.BanListEntry m_7524_()Z
public net.minecraft.world.BossEvent f_18843_
public net.minecraft.world.BossEvent f_18842_
public net.minecraft.world.BossEvent f_18840_
public net.minecraft.world.level.block.entity.CampfireBlockEntity f_59044_
public net.minecraft.world.level.block.entity.CampfireBlockEntity f_59043_
public net.minecraft.world.entity.npc.Villager m_35528_()V
public net.minecraft.world.item.context.UseOnContext <init>(Lnet/minecraft/world/level/Level;Lnet/minecraft/world/entity/player/Player;Lnet/minecraft/world/InteractionHand;Lnet/minecraft/world/item/ItemStack;Lnet/minecraft/world/phys/BlockHitResult;)V
public net.minecraft.network.protocol.game.ClientboundSetDefaultSpawnPositionPacket f_133111_
public net.minecraft.world.entity.vehicle.AbstractMinecartContainer f_38204_
public net.minecraft.world.entity.vehicle.AbstractMinecartContainer f_38205_
public net.minecraft.world.level.block.entity.BeehiveBlockEntity f_58733_
public net.minecraft.world.level.chunk.LevelChunkSection f_62967_
public net.minecraft.ChatFormatting f_126622_
public net.minecraft.commands.CommandSourceStack f_81288_
public net.minecraft.world.phys.shapes.IndexMerger
public net.minecraft.world.level.block.entity.LecternBlockEntity f_59525_
public net.minecraft.world.level.block.entity.LecternBlockEntity m_59532_(I)V
public net.minecraftforge.registries.NamespacedWrapper
public net.minecraft.world.inventory.AnvilMenu f_39002_
public net.minecraft.world.inventory.AnvilMenu f_39001_
public net.minecraft.world.level.block.state.StateHolder f_61110_
public net.minecraft.world.level.storage.LevelStorageSource f_78194_
# Bukkit 1.17
public net.minecraft.world.level.block.ComposterBlock$InputContainer
public net.minecraft.world.level.block.ComposterBlock$OutputContainer
public net.minecraft.server.level.ServerLevel m_142646_()Lnet/minecraft/world/level/entity/LevelEntityGetter;
public net.minecraft.world.level.Level m_142646_()Lnet/minecraft/world/level/entity/LevelEntityGetter;
public net.minecraft.client.multiplayer.ClientLevel m_142646_()Lnet/minecraft/world/level/entity/LevelEntityGetter;
public net.minecraft.world.entity.Mob m_7582_()Lnet/minecraft/resources/ResourceLocation;
public net.minecraft.world.entity.monster.Slime m_7582_()Lnet/minecraft/resources/ResourceLocation;
public net.minecraft.world.entity.monster.MagmaCube m_7582_()Lnet/minecraft/resources/ResourceLocation;
public net.minecraft.server.MinecraftServer m_6681_(Ljava/lang/Runnable;)Lnet/minecraft/server/TickTask;
public net.minecraft.server.level.ServerBossEvent m_143224_(Ljava/util/function/Function;)V
public net.minecraft.server.level.ServerPlayer m_143399_(Lnet/minecraft/world/inventory/AbstractContainerMenu;)V
public net.minecraft.server.level.ServerLevel f_143244_
public net.minecraft.util.datafix.fixes.BlockStateData m_14942_(ILjava/lang/String;[Ljava/lang/String;)V
public net.minecraft.world.entity.Entity m_146912_()V
public net.minecraft.world.entity.Entity f_146813_
public net.minecraft.world.entity.GlowSquid m_147119_(I)V
public net.minecraft.world.entity.ai.attributes.RangedAttribute f_22308_
public net.minecraft.world.entity.animal.Ocelot m_29038_()Z
public net.minecraft.world.entity.animal.Ocelot m_29045_(Z)V
public net.minecraft.world.entity.animal.axolotl.Axolotl m_149117_(Lnet/minecraft/world/entity/animal/axolotl/Axolotl$Variant;)V
public net.minecraft.world.entity.monster.Creeper f_32270_
public net.minecraft.world.entity.monster.Shulker m_33463_()I
public net.minecraft.world.entity.monster.Shulker m_149788_(Lnet/minecraft/core/Direction;)V
public net.minecraft.world.entity.monster.Shulker m_33418_(I)V
public net.minecraft.world.entity.monster.Skeleton f_149828_
public net.minecraft.world.entity.monster.Skeleton m_149830_(I)V
public net.minecraft.world.entity.monster.piglin.Piglin f_34678_
public net.minecraft.world.entity.npc.Villager m_35518_()V
public net.minecraft.world.entity.projectile.FishingHook m_150155_(Lnet/minecraft/world/entity/Entity;)V
public net.minecraft.world.entity.projectile.FishingHook m_150157_(Lnet/minecraft/world/entity/Entity;)V
public net.minecraft.world.entity.projectile.LargeFireball f_37197_
public net.minecraft.world.item.MapItem m_151120_(Lnet/minecraft/world/level/Level;IIIZZLnet/minecraft/resources/ResourceKey;)I
public net.minecraft.world.level.BaseSpawner m_151332_(Lnet/minecraft/world/level/Level;Lnet/minecraft/core/BlockPos;)Lnet/minecraft/resources/ResourceLocation;
public net.minecraft.world.level.Level f_46438_
public net.minecraft.world.level.Level f_46440_
public net.minecraft.world.level.block.Block m_49805_(Lnet/minecraft/server/level/ServerLevel;Lnet/minecraft/core/BlockPos;I)V
public net.minecraft.world.level.block.FireBlock f_53421_
public net.minecraft.world.level.block.entity.SculkSensorBlockEntity f_155633_
public net.minecraft.world.level.block.entity.BarrelBlockEntity f_155050_
public net.minecraft.world.level.block.entity.ChestBlockEntity f_155324_
public net.minecraft.world.level.block.entity.ChestBlockEntity m_155338_(Lnet/minecraft/world/level/Level;Lnet/minecraft/core/BlockPos;Lnet/minecraft/world/level/block/state/BlockState;Lnet/minecraft/sounds/SoundEvent;)V
public net.minecraft.world.level.saveddata.maps.MapItemSavedData f_77893_
public net.minecraft.world.level.saveddata.maps.MapItemSavedData f_77894_
public net.minecraft.world.level.saveddata.maps.MapItemSavedData m_164812_()V
public net.minecraft.world.level.saveddata.maps.MapItemSavedData m_164789_(II)V
public-f net.minecraft.network.protocol.handshake.ClientIntentionPacket f_134721_
public-f net.minecraft.world.inventory.AbstractContainerMenu f_150394_
public-f net.minecraft.world.level.saveddata.maps.MapItemSavedData f_77887_
public-f net.minecraft.world.level.saveddata.maps.MapItemSavedData f_77892_
public-f net.minecraft.world.level.saveddata.maps.MapItemSavedData f_77890_
public-f net.minecraft.world.level.saveddata.maps.MapItemSavedData f_77888_
public-f net.minecraft.world.level.saveddata.maps.MapItemSavedData f_77889_
public-f net.minecraft.world.level.saveddata.maps.MapItemSavedData f_77885_
public-f net.minecraft.world.level.saveddata.maps.MapItemSavedData f_77886_
public net.minecraft.server.MinecraftServer f_129750_
public net.minecraft.util.datafix.fixes.BlockStateData m_14942_(ILjava/lang/String;[Ljava/lang/String;)V
public net.minecraft.world.entity.monster.Skeleton f_149826_
public net.minecraft.world.scores.criteria.ObjectiveCriteria f_166108_
# Bukkit 1.18
public net.minecraft.world.level.Level m_142425_(Lnet/minecraft/world/level/entity/EntityTypeTest;Lnet/minecraft/world/phys/AABB;Ljava/util/function/Predicate;)Ljava/util/List;
public net.minecraft.server.Main m_195488_(Lnet/minecraft/world/level/storage/LevelStorageSource$LevelStorageAccess;Lcom/mojang/datafixers/DataFixer;ZLjava/util/function/BooleanSupplier;Lnet/minecraft/world/level/levelgen/WorldGenSettings;)V
public net.minecraft.server.MinecraftServer m_6681_(Ljava/lang/Runnable;)Ljava/lang/Runnable;
public net.minecraft.util.datafix.fixes.BlockStateData m_14942_(ILjava/lang/String;[Ljava/lang/String;)V
public net.minecraft.world.entity.monster.Guardian m_32817_(I)V
public net.minecraft.world.inventory.CraftingMenu f_39348_
public net.minecraft.world.inventory.CraftingMenu f_39349_
# public net.minecraft.world.item.BucketItem f_40687_
public net.minecraft.world.level.biome.Biome m_47505_(Lnet/minecraft/core/BlockPos;)F
public net.minecraft.world.level.chunk.ChunkGenerator f_62140_
public net.minecraft.world.level.chunk.PalettedContainer f_63071_
public net.minecraft.world.level.chunk.ChunkAccess f_187610_
public net.minecraft.world.level.chunk.ChunkAccess f_187608_
public net.minecraft.world.level.chunk.storage.ChunkSerializer f_188227_
public net.minecraft.world.level.chunk.storage.EntityStorage f_182485_
public net.minecraft.world.level.chunk.storage.EntityStorage f_156538_
public net.minecraft.world.level.entity.PersistentEntitySectionManager f_157493_
public net.minecraft.world.level.entity.PersistentEntitySectionManager m_157555_(J)V
public net.minecraft.world.level.levelgen.NoiseBasedChunkGenerator f_188604_
public net.minecraft.world.level.levelgen.NoiseBasedChunkGenerator f_64318_
public net.minecraft.world.level.levelgen.structure.templatesystem.StructureTemplate f_74483_
public net.minecraft.world.level.levelgen.structure.templatesystem.StructureTemplate f_74482_
public net.minecraft.world.level.levelgen.structure.templatesystem.StructureManager m_74348_(Lnet/minecraft/resources/ResourceLocation;Ljava/lang/String;)Ljava/nio/file/Path;
public net.minecraft.world.level.levelgen.structure.templatesystem.StructureManager m_163776_(Lnet/minecraft/resources/ResourceLocation;)Ljava/util/Optional;
public net.minecraft.world.level.levelgen.structure.templatesystem.StructureManager m_74337_(Ljava/io/InputStream;)Lnet/minecraft/world/level/levelgen/structure/templatesystem/StructureTemplate;
public net.minecraft.world.level.levelgen.structure.templatesystem.StructureManager m_163778_(Lnet/minecraft/resources/ResourceLocation;)Ljava/util/Optional;
public net.minecraft.world.level.levelgen.structure.templatesystem.StructureManager f_74326_
public net.minecraft.world.level.material.MaterialColor f_76387_
# Bukkit 1.19
public net.minecraft.util.datafix.fixes.BlockStateData m_14942_(ILjava/lang/String;[Ljava/lang/String;)V
public net.minecraft.world.entity.animal.frog.Tadpole f_218680_
public net.minecraft.world.entity.animal.goat.Goat f_218750_
public net.minecraft.world.entity.animal.goat.Goat f_218751_
public net.minecraft.world.entity.animal.horse.SkeletonHorse f_30892_
public net.minecraft.world.entity.decoration.Painting m_218891_(Lnet/minecraft/core/Holder;)V
public net.minecraft.world.entity.monster.Vex f_33978_
public net.minecraft.world.entity.monster.Vex f_33979_
public net.minecraft.world.entity.monster.Vindicator f_34071_
public net.minecraft.world.entity.npc.Villager m_35524_()V
public net.minecraft.world.entity.vehicle.Boat f_38279_
public net.minecraft.world.inventory.AnvilMenu f_39000_
# public net.minecraft.world.item.BucketItem f_40687_
public net.minecraft.world.item.trading.MerchantOffer f_45317_
public net.minecraft.world.item.trading.MerchantOffer f_45316_
public net.minecraft.world.level.block.FireBlock f_221147_
public net.minecraft.world.level.block.entity.SculkShriekerBlockEntity f_222831_
public net.minecraft.world.level.block.entity.EnderChestBlockEntity f_155511_
public net.minecraft.world.level.block.entity.AbstractFurnaceBlockEntity f_58320_
public net.minecraft.world.level.block.state.properties.IntegerProperty f_223001_
public net.minecraft.world.level.block.state.properties.IntegerProperty f_223000_
public net.minecraft.world.level.levelgen.structure.placement.StructurePlacement f_227026_
public net.minecraft.world.level.levelgen.structure.placement.StructurePlacement f_227024_
public net.minecraft.world.level.levelgen.structure.placement.StructurePlacement f_227023_
public net.minecraft.world.level.levelgen.structure.placement.StructurePlacement f_227022_
public net.minecraft.world.level.levelgen.structure.placement.StructurePlacement f_227025_
public net.minecraft.world.level.levelgen.structure.templatesystem.StructureTemplateManager m_230411_(Ljava/nio/file/Path;Lnet/minecraft/resources/ResourceLocation;Ljava/lang/String;)Ljava/nio/file/Path;
public net.minecraft.world.level.levelgen.structure.templatesystem.StructureTemplateManager m_230431_(Lnet/minecraft/resources/ResourceLocation;)Ljava/util/Optional;
public net.minecraft.world.level.levelgen.structure.templatesystem.StructureTemplateManager m_230427_(Lnet/minecraft/resources/ResourceLocation;)Ljava/util/Optional;
public net.minecraft.world.level.levelgen.structure.templatesystem.StructureTemplateManager m_230404_(Lnet/minecraft/nbt/CompoundTag;)Lnet/minecraft/world/level/levelgen/structure/templatesystem/StructureTemplate;
public net.minecraft.world.level.levelgen.structure.templatesystem.StructureTemplateManager f_230345_
public net.minecraft.world.level.storage.LevelStorageSource$LevelStorageAccess f_230867_
public-f net.minecraft.server.ReloadableServerResources f_206847_
# Bukkit 1.19.2
public net.minecraft.world.entity.animal.allay.Allay f_238682_ # jukeboxPos
public net.minecraft.world.entity.animal.allay.Allay f_238791_ # duplicationCooldown
public net.minecraft.world.entity.animal.allay.Allay m_239811_()V # resetDuplicationCooldown
public net.minecraft.world.entity.animal.allay.Allay m_218324_()Z # canDuplicate
public net.minecraft.world.entity.projectile.FireworkRocketEntity f_37022_ # life
public net.minecraft.world.entity.projectile.FireworkRocketEntity f_37024_ # attachedToEntity
# Bukkit 1.19.3
public net.minecraft.server.MinecraftServer$ReloadableResources
public net.minecraft.server.dedicated.DedicatedServerProperties$WorldDimensionData
public net.minecraft.server.level.ServerLevel m_261178_(Lnet/minecraft/world/level/entity/EntityTypeTest;Ljava/util/function/Predicate;Ljava/util/List;I)V
public net.minecraft.server.Main m_195488_(Lnet/minecraft/world/level/storage/LevelStorageSource$LevelStorageAccess;Lcom/mojang/datafixers/DataFixer;ZLjava/util/function/BooleanSupplier;Lnet/minecraft/core/Registry;)V
public net.minecraft.server.WorldLoader m_245736_(Lnet/minecraft/server/packs/resources/ResourceManager;Lnet/minecraft/core/LayeredRegistryAccess;Lnet/minecraft/server/RegistryLayer;Ljava/util/List;)Lnet/minecraft/core/LayeredRegistryAccess;
public net.minecraft.util.datafix.fixes.BlockStateData m_14942_(ILjava/lang/String;[Ljava/lang/String;)V
public net.minecraft.world.entity.animal.TropicalFish m_30042_()I
public net.minecraft.world.entity.animal.TropicalFish m_30056_(I)V
public net.minecraft.world.entity.decoration.ItemFrame f_31757_
public net.minecraft.world.entity.decoration.ItemFrame f_31758_
public net.minecraft.world.entity.projectile.FireworkRocketEntity f_37024_
public net.minecraft.world.entity.projectile.FireworkRocketEntity f_37022_
public net.minecraft.world.item.context.UseOnContext <init>(Lnet/minecraft/world/entity/player/Player;Lnet/minecraft/world/InteractionHand;Lnet/minecraft/world/phys/BlockHitResult;)V
public net.minecraft.world.level.block.entity.ChiseledBookShelfBlockEntity f_262317_
public net.minecraft.world.level.chunk.ChunkGenerator f_223021_
public-f net.minecraft.world.level.saveddata.maps.MapItemSavedData f_256718_
public-f net.minecraft.world.level.saveddata.maps.MapItemSavedData f_256789_
# Bukkit 1.19.4
public net.minecraft.world.entity.Interaction$PlayerAction
public net.minecraft.world.entity.monster.Guardian$GuardianAttackGoal
public net.minecraft.world.level.biome.Biome$ClimateSettings
public net.minecraft.server.network.ServerLoginPacketListenerImpl f_10013_
public net.minecraft.util.datafix.fixes.BlockStateData m_14942_(ILjava/lang/String;[Ljava/lang/String;)V
public net.minecraft.world.entity.Display$ItemDisplay m_269362_(Lnet/minecraft/world/item/ItemStack;)V
public net.minecraft.world.entity.Display$ItemDisplay m_269028_(Lnet/minecraft/world/item/ItemDisplayContext;)V
public net.minecraft.world.entity.Display$TextDisplay m_269173_(F)I
public net.minecraft.world.entity.Display$TextDisplay m_269180_()B
public net.minecraft.world.entity.Display$TextDisplay m_269001_(I)V
public net.minecraft.world.entity.Display$TextDisplay m_269559_(B)V
public net.minecraft.world.entity.Display$TextDisplay m_269174_(I)V
public net.minecraft.world.entity.Display$TextDisplay m_269037_(Lnet/minecraft/network/chat/Component;)V
public net.minecraft.world.entity.Display$TextDisplay m_269007_(B)V
public net.minecraft.world.entity.Display m_269448_(Lnet/minecraft/network/syncher/SynchedEntityData;)Lcom/mojang/math/Transformation;
public net.minecraft.world.entity.Display m_269102_()Lnet/minecraft/util/Brightness;
public net.minecraft.world.entity.Display m_269034_()I
public net.minecraft.world.entity.Display m_269410_()F
public net.minecraft.world.entity.Display m_276347_()I
public net.minecraft.world.entity.Display m_269272_()I
public net.minecraft.world.entity.Display m_269459_()F
public net.minecraft.world.entity.Display m_269072_(F)F
public net.minecraft.world.entity.Display m_269081_()F
public net.minecraft.world.entity.Display m_269558_()F
public net.minecraft.world.entity.Display m_269423_(Lnet/minecraft/world/entity/Display$BillboardConstraints;)V
public net.minecraft.world.entity.Display m_269586_(Lnet/minecraft/util/Brightness;)V
public net.minecraft.world.entity.Display m_269026_(I)V
public net.minecraft.world.entity.Display m_269087_(F)V
public net.minecraft.world.entity.Display m_276345_(I)V
public net.minecraft.world.entity.Display m_269317_(I)V
public net.minecraft.world.entity.Display m_269526_(F)V
public net.minecraft.world.entity.Display m_269228_(F)V
public net.minecraft.world.entity.Display m_269214_(Lcom/mojang/math/Transformation;)V
public net.minecraft.world.entity.Display m_269215_(F)V
public net.minecraft.world.entity.Display m_269441_(F)V
public net.minecraft.world.entity.Interaction f_271404_
public net.minecraft.world.entity.Interaction m_271858_()F
public net.minecraft.world.entity.Interaction m_271819_()Z
public net.minecraft.world.entity.Interaction m_272023_()F
public net.minecraft.world.entity.Interaction f_271193_
public net.minecraft.world.entity.Interaction m_271774_(F)V
public net.minecraft.world.entity.Interaction m_271717_(Z)V
public net.minecraft.world.entity.Interaction m_272058_(F)V
public net.minecraft.world.entity.ItemBasedSteering f_20835_
public net.minecraft.world.entity.ItemBasedSteering m_274397_()I
public net.minecraft.world.entity.ItemBasedSteering f_20834_
public net.minecraft.world.entity.item.ItemEntity f_265881_
public net.minecraft.world.entity.item.ItemEntity f_31988_
public net.minecraft.world.entity.monster.Guardian$GuardianAttackGoal f_32868_
# public net.minecraft.world.level.biome.Biome f_47437_
public net.minecraft.world.level.block.entity.SuspiciousSandBlockEntity f_271119_
public net.minecraft.world.level.block.entity.SuspiciousSandBlockEntity f_271461_
public net.minecraft.world.level.block.entity.SuspiciousSandBlockEntity f_271354_
# Bukkit 1.20
public net.minecraft.server.level.ServerLevel m_260813_(Lnet/minecraft/world/level/entity/EntityTypeTest;Ljava/util/function/Predicate;Ljava/util/List;)V
public net.minecraft.server.packs.repository.Pack f_244124_
public net.minecraft.util.datafix.fixes.BlockStateData m_14942_(ILjava/lang/String;[Ljava/lang/String;)V
public net.minecraft.world.entity.Display$BlockDisplay m_269134_()Lnet/minecraft/world/level/block/state/BlockState;
public net.minecraft.world.entity.Display$BlockDisplay m_269329_(Lnet/minecraft/world/level/block/state/BlockState;)V
public net.minecraft.world.entity.Display$ItemDisplay m_269568_()Lnet/minecraft/world/item/ItemStack;
public net.minecraft.world.entity.Display$ItemDisplay m_269386_()Lnet/minecraft/world/item/ItemDisplayContext;
public net.minecraft.world.entity.Display$TextDisplay m_269375_()I
public net.minecraft.world.entity.Display$TextDisplay m_269327_()B
public net.minecraft.world.entity.Display$TextDisplay m_269517_()I
public net.minecraft.world.entity.Display$TextDisplay m_269000_()Lnet/minecraft/network/chat/Component;
public net.minecraft.world.entity.Display m_269218_()Lnet/minecraft/world/entity/Display$BillboardConstraints;
public net.minecraft.world.entity.Display m_269155_()F
public net.minecraft.world.entity.animal.sniffer.Sniffer m_271905_()Ljava/util/Optional;
public net.minecraft.world.entity.animal.sniffer.Sniffer m_272270_()Z
public net.minecraft.world.entity.animal.sniffer.Sniffer m_272217_()Ljava/util/stream/Stream;
public net.minecraft.world.entity.animal.sniffer.Sniffer m_271917_()Lnet/minecraft/world/entity/animal/sniffer/Sniffer$State;
public net.minecraft.world.entity.animal.sniffer.Sniffer m_271874_(Lnet/minecraft/core/BlockPos;)Lnet/minecraft/world/entity/animal/sniffer/Sniffer;
public net.minecraft.world.entity.item.FallingBlockEntity f_31940_
public net.minecraft.world.entity.item.FallingBlockEntity f_149641_
public net.minecraft.world.entity.player.Player f_36081_
public net.minecraft.world.entity.vehicle.MinecartTNT m_38688_(D)V
public net.minecraft.world.entity.vehicle.MinecartTNT f_38647_
public net.minecraft.world.flag.FeatureFlag f_244012_
public net.minecraft.world.flag.FeatureFlag f_243952_
public net.minecraft.world.flag.FeatureFlagRegistry f_244560_
public net.minecraft.world.item.DebugStickItem m_150802_(Lnet/minecraft/world/entity/player/Player;Lnet/minecraft/world/level/block/state/BlockState;Lnet/minecraft/world/level/LevelAccessor;Lnet/minecraft/core/BlockPos;ZLnet/minecraft/world/item/ItemStack;)Z
public net.minecraft.world.level.StructureManager f_220460_
public net.minecraft.world.level.block.entity.BrushableBlockEntity f_276563_
public net.minecraft.world.level.block.entity.BrushableBlockEntity f_276466_
public net.minecraft.world.level.block.entity.BrushableBlockEntity f_276487_
public net.minecraft.world.level.block.entity.DecoratedPotBlockEntity f_283890_
public net.minecraft.world.level.block.entity.BellBlockEntity f_58818_
public net.minecraft.world.level.block.entity.BellBlockEntity f_58819_
public net.minecraft.world.level.block.entity.JukeboxBlockEntity f_238637_
public net.minecraft.world.level.block.entity.JukeboxBlockEntity f_238572_
public net.minecraft.world.level.block.entity.JukeboxBlockEntity f_238695_
public net.minecraft.world.level.block.entity.SignBlockEntity f_59722_
public net.minecraft.world.level.block.entity.SkullBlockEntity f_262250_
public net.minecraft.world.level.material.MapColor f_283862_
private-f net.minecraft.server.MinecraftServer f_129762_
