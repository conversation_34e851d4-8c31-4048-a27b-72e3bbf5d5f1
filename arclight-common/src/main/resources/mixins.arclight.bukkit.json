{"required": true, "minVersion": "0.8", "package": "io.izzel.arclight.common.mixin.bukkit", "target": "@env(DEFAULT)", "refmap": "mixins.arclight.refmap.json", "setSourceFile": true, "plugin": "io.izzel.arclight.common.mod.ArclightMixinPlugin", "injectors": {"defaultRequire": 1}, "compatibilityLevel": "JAVA_17", "mixins": ["ColouredConsoleSenderMixin", "CraftAbstractVillagerMixin", "CraftBlockMixin", "CraftBlockStateMixin", "CraftChatMessageMixin", "CraftConsoleCommandSenderMixin", "CraftEnderDragonMixin", "CraftEntityMixin", "CraftEventFactoryMixin", "CraftHumanEntityMixin", "CraftInventoryMixin", "CraftInventoryViewMixin", "CraftItemFactoryMixin", "CraftItemStackMixin", "CraftLegacyLegacyMixin", "CraftLegacyUtilMixin", "CraftMagicNumbersMixin", "CraftMetaItemMixin", "CraftPlayerMixin", "CraftPotionUtilMixin", "CraftRecipeMixin", "CraftRegionAccessorMixin", "CraftSchedulerMixin", "CraftServerMixin", "CraftSpawnCategoryMixin", "CraftSpellcasterMixin", "CraftVillagerMixin", "CraftWorldMixin", "EntityTypeMixin", "JavaPluginLoaderMixin", "JavaPluginMixin", "LibraryLoaderMixin", "MaterialMixin", "PluginClassLoaderMixin", "PotionEffectTypeMixin", "RecipeIteratorMixin", "Registry_SimpleRegistryMixin", "StandardMessengerMixin", "WatchdogThreadMixin", "adventure.ChatColorAdventureMixin", "adventure.CommandSenderAdventureMixin", "adventure.CraftMagicNumbersAdventureMixin", "adventure.CraftPlayerAdventureMixin", "adventure.CraftServerAdventureMixin", "adventure.KeyedAdventureMixin", "adventure.NameableAdventureMixin", "adventure.NamespacedKeyAdventureMixin"]}