{"minVersion": "0.8", "package": "io.izzel.arclight.common.mixin.optimization.general", "target": "@env(DEFAULT)", "refmap": "mixins.arclight.refmap.json", "plugin": "io.izzel.arclight.common.mod.ArclightMixinPlugin", "compatibilityLevel": "JAVA_17", "mixins": ["ClassInheritanceMultiMapMixin", "EntityDataManagerMixin_Optimize", "EntityMixin_EnhancedOptimize", "EntityMixin_Optimize", "GoalMixin", "MobMixin_Optimization", "ServerLevelMixin_Optimize", "VoxelShapesMixin", "chunkload.SleepInBedMixin_Optimize", "network.ChunkMapMixin_Optimize", "network.ChunkMapMixin_Optimize$TrackedEntityMixin", "network.ServerEntityMixin_EnhancedOptimize", "network.ServerGamePacketListenerImplMixin_Optimize", "network.ServerPlayerMixin_Optimize", "realtime.ItemEntityMixin_Realtime", "realtime.PlayerInteractionManagerMixin_Realtime"]}