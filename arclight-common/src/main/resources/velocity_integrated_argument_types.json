{"version": "1.20.1", "entries": ["brigadier:bool", "brigadier:float", "brigadier:double", "brigadier:integer", "brigadier:long", "brigadier:string", "minecraft:entity", "minecraft:game_profile", "minecraft:block_pos", "minecraft:column_pos", "minecraft:vec3", "minecraft:vec2", "minecraft:block_state", "minecraft:block_predicate", "minecraft:item_stack", "minecraft:item_predicate", "minecraft:color", "minecraft:component", "minecraft:message", "minecraft:nbt_compound_tag", "minecraft:nbt_tag", "minecraft:nbt_path", "minecraft:objective", "minecraft:objective_criteria", "minecraft:operation", "minecraft:particle", "minecraft:angle", "minecraft:rotation", "minecraft:scoreboard_slot", "minecraft:score_holder", "minecraft:swizzle", "minecraft:team", "minecraft:item_slot", "minecraft:resource_location", "minecraft:function", "minecraft:entity_anchor", "minecraft:int_range", "minecraft:float_range", "minecraft:dimension", "minecraft:gamemode", "minecraft:time", "minecraft:resource_or_tag", "minecraft:resource_or_tag_key", "minecraft:resource", "minecraft:resource_key", "minecraft:template_mirror", "minecraft:template_rotation", "minecraft:heightmap", "minecraft:uuid"]}