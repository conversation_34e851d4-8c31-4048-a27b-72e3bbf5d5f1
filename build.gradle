import io.izzel.arclight.gradle.tasks.UploadFilesTask

allprojects {
    group 'io.izzel.luminara'
    version '1.0.8-PRE3'

    def getGitHash = { ->
        def stdout = new ByteArrayOutputStream()
        exec {
            commandLine 'git', 'rev-parse', '--short', 'HEAD'
            standardOutput = stdout
        }
        return stdout.toString().trim()
    }

    ext {
        agpVersion = '1.23'
        minecraftVersion = '1.20.1'
        forgeVersion = '47.4.4'
        apiVersion = '1.5.4'
        toolsVersion = '1.3.0'
        mixinVersion = '0.8.5'
        versionName = 'trials'
        gitHash = getGitHash()
    }

    task cleanBuild {
        doFirst {
            project.file("build/libs").deleteDir()
        }
    }
}

task collect(type: Copy) {
    destinationDir = file('build/libs')
    from { project(':arclight-forge').tasks.jar.outputs }
    dependsOn { project(':arclight-forge').tasks.jar }
}

def gitBranch() {
    def stdout = new ByteArrayOutputStream()
    exec {
        commandLine 'git', 'rev-parse', '--abbrev-ref', 'HEAD'
        standardOutput = stdout
    }
    return stdout.toString().trim()
}

tasks.register('uploadFiles', UploadFilesTask) {
    mcVersion.set project.ext.minecraftVersion
    version.set "${project.version}-${project.ext.gitHash}"
    snapshot.set project.version.toString().endsWith("-SNAPSHOT")
    gitHash.set project.ext.gitHash
    branch.set gitBranch()
    inputs.files tasks.collect.outputs.files
    dependsOn(tasks.collect)
}
