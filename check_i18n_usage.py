#!/usr/bin/env python3
"""
Luminara i18n Key Usage Checker
检查i18n配置文件中定义但未在代码中使用的消息键
"""

import os
import re
import glob
from pathlib import Path
from typing import Set, Dict, List, Tuple

class I18nChecker:
    def __init__(self, project_root: str = "."):
        self.project_root = Path(project_root)
        self.i18n_keys = set()
        self.used_keys = set()
        self.unused_keys = set()
        
    def parse_hocon_file(self, file_path: Path) -> Set[str]:
        """解析HOCON配置文件，提取所有的键"""
        keys = set()
        
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
                
            # 移除注释
            content = re.sub(r'#.*$', '', content, flags=re.MULTILINE)
            
            # 解析嵌套的键
            current_path = []
            
            for line in content.split('\n'):
                line = line.strip()
                if not line:
                    continue
                    
                # 检查是否是块开始
                if line.endswith('{'):
                    block_name = line.replace('{', '').strip()
                    if block_name:
                        current_path.append(block_name)
                elif line == '}':
                    if current_path:
                        current_path.pop()
                else:
                    # 检查键值对
                    if '=' in line:
                        key_part = line.split('=')[0].strip()
                        # 移除引号
                        key_part = key_part.strip('"\'')
                        
                        if current_path:
                            full_key = '.'.join(current_path + [key_part])
                        else:
                            full_key = key_part
                            
                        keys.add(full_key)
                        
        except Exception as e:
            print(f"Error parsing {file_path}: {e}")
            
        return keys
    
    def find_i18n_keys(self) -> Set[str]:
        """从zh_cn.conf文件中提取所有i18n键"""
        zh_cn_file = self.project_root / "i18n-config/src/main/resources/META-INF/i18n/zh_cn.conf"
        
        if not zh_cn_file.exists():
            print(f"Warning: {zh_cn_file} not found")
            return set()
            
        return self.parse_hocon_file(zh_cn_file)
    
    def find_used_keys(self) -> Set[str]:
        """在Java代码中查找使用的i18n键"""
        used_keys = set()
        
        # 搜索Java文件
        java_files = list(self.project_root.glob("**/*.java"))
        
        for java_file in java_files:
            try:
                with open(java_file, 'r', encoding='utf-8') as f:
                    content = f.read()
                    
                # 查找LOGGER调用中的i18n键
                # 匹配模式: LOGGER.info("key", ...) 或 ARCLIGHT_LOGGER.warn("key", ...)
                patterns = [
                    r'LOGGER\.\w+\s*\(\s*"([^"]+)"',
                    r'ARCLIGHT_LOGGER\.\w+\s*\(\s*"([^"]+)"',
                    r'ArclightMod\.LOGGER\.\w+\s*\(\s*"([^"]+)"',
                ]
                
                for pattern in patterns:
                    matches = re.findall(pattern, content)
                    for match in matches:
                        # 过滤掉明显不是i18n键的字符串
                        if '.' in match and not match.startswith('http') and not match.endswith('.java'):
                            used_keys.add(match)
                            
            except Exception as e:
                print(f"Error reading {java_file}: {e}")
                
        return used_keys
    
    def check_usage(self) -> Tuple[Set[str], Set[str], Set[str]]:
        """检查i18n键的使用情况"""
        print("🔍 正在扫描i18n键定义...")
        self.i18n_keys = self.find_i18n_keys()
        print(f"📋 找到 {len(self.i18n_keys)} 个i18n键定义")
        
        print("\n🔍 正在扫描代码中的使用情况...")
        self.used_keys = self.find_used_keys()
        print(f"✅ 找到 {len(self.used_keys)} 个已使用的键")
        
        # 计算未使用的键
        self.unused_keys = self.i18n_keys - self.used_keys
        
        # 计算未定义但被使用的键
        undefined_keys = self.used_keys - self.i18n_keys
        
        return self.unused_keys, undefined_keys, self.i18n_keys & self.used_keys
    
    def print_results(self):
        """打印检查结果"""
        unused_keys, undefined_keys, used_keys = self.check_usage()
        
        print("\n" + "="*60)
        print("📊 I18N 键使用情况报告")
        print("="*60)
        
        print(f"\n📈 统计信息:")
        print(f"  总定义键数: {len(self.i18n_keys)}")
        print(f"  已使用键数: {len(used_keys)}")
        print(f"  未使用键数: {len(unused_keys)}")
        print(f"  未定义键数: {len(undefined_keys)}")
        print(f"  使用率: {len(used_keys)/len(self.i18n_keys)*100:.1f}%")
        
        if unused_keys:
            print(f"\n❌ 未使用的键 ({len(unused_keys)} 个):")
            for key in sorted(unused_keys):
                print(f"  - {key}")
        else:
            print(f"\n✅ 所有定义的键都已被使用！")
            
        if undefined_keys:
            print(f"\n⚠️  未定义但被使用的键 ({len(undefined_keys)} 个):")
            for key in sorted(undefined_keys):
                print(f"  - {key}")
        else:
            print(f"\n✅ 所有使用的键都已定义！")
            
        # 按类别分组显示未使用的键
        if unused_keys:
            print(f"\n📂 按类别分组的未使用键:")
            categories = {}
            for key in unused_keys:
                category = key.split('.')[0] if '.' in key else 'root'
                if category not in categories:
                    categories[category] = []
                categories[category].append(key)
                
            for category, keys in sorted(categories.items()):
                print(f"\n  {category} ({len(keys)} 个):")
                for key in sorted(keys):
                    print(f"    - {key}")
    
    def sync_language_files(self):
        """同步所有语言文件，确保它们有相同的键结构"""
        print("\n🔄 正在同步语言文件...")
        
        i18n_dir = self.project_root / "i18n-config/src/main/resources/META-INF/i18n"
        zh_cn_file = i18n_dir / "zh_cn.conf"
        
        if not zh_cn_file.exists():
            print(f"❌ 源文件不存在: {zh_cn_file}")
            return
            
        # 获取所有语言文件
        lang_files = list(i18n_dir.glob("*.conf"))
        lang_files = [f for f in lang_files if f.name != "zh_cn.conf"]
        
        print(f"📁 找到 {len(lang_files)} 个其他语言文件")
        
        # 解析zh_cn文件的结构
        zh_keys = self.parse_hocon_file(zh_cn_file)
        
        for lang_file in lang_files:
            print(f"🔄 检查 {lang_file.name}...")
            lang_keys = self.parse_hocon_file(lang_file)
            
            missing_keys = zh_keys - lang_keys
            extra_keys = lang_keys - zh_keys
            
            if missing_keys:
                print(f"  ❌ 缺少 {len(missing_keys)} 个键")
                for key in sorted(missing_keys):
                    print(f"    - {key}")
            
            if extra_keys:
                print(f"  ⚠️  多出 {len(extra_keys)} 个键")
                for key in sorted(extra_keys):
                    print(f"    - {key}")
                    
            if not missing_keys and not extra_keys:
                print(f"  ✅ 键结构一致")

def main():
    """主函数"""
    print("🚀 Luminara I18N 键使用检查器")
    print("="*40)
    
    checker = I18nChecker()
    
    # 检查使用情况
    checker.print_results()
    
    # 同步语言文件
    checker.sync_language_files()
    
    print(f"\n✨ 检查完成！")

if __name__ == "__main__":
    main()
