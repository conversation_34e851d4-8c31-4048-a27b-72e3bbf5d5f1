




mixin-load {
  core = [EN_US] "核心 Mixin 配置已加载"
  optimization = [EN_US] "优化 Mixin 配置已加载"
}


patcher {
  loading = [EN_US] "正在加载 Plugin Patcher ..."
  loaded = [EN_US] "加载了 {} 个 Patcher"
  load-error = [EN_US] "加载 Patcher 时发生错误"
}
registry {
  forge-event = [EN_US] "Luminara 事件系统已注册"
  begin = [EN_US] "正在向 Bukkit 注册 ..."
  error = [EN_US] "处理 Forge 注册时出错 "
  enchantment = [EN_US] "注册了 {} 个新的附魔"
  potion = [EN_US] "注册了 {} 个新的药水效果"
  material = [EN_US] "注册了 {} 个材料，其中 {} 个方块 {} 个物品"
  entity-type = [EN_US] "注册了 {} 个新的生物类型"
  environment = [EN_US] "注册了 {} 个新的世界类型"
  villager-profession = [EN_US] "注册了 {} 个新的村民职业"
  biome = [EN_US] "注册了 {} 个新的生物群系"
  meta-type {
    error = [EN_US] "{} 提供的 itemMetaType {} 无效: {}"
    no-candidate = [EN_US] "{} 未在提供的 itemMetaType {} 找到合适的构造方法"
  }
  block-state {
    error = [EN_US] "{} 提供的 itemMetaType {} 无效: {}"
    no-candidate = [EN_US] "{} 未在提供的 blockStateClass {} 找到合适的构造方法"
  }
  entity {
    error = [EN_US] "{} 提供的 entityClass {} 无效: {}"
  }
  debug {
    registered-cooking-category = [EN_US] "已注册 {} 为烹饪类别 {}"
    registered-spawn-category = [EN_US] "已注册 {} 为生成类别 {}"
    registered-custom-stats = [EN_US] "已注册 {} 为自定义统计 {}"
    registered-biome = [EN_US] "已注册 {} 为生物群系 {}"
    registered-environment = [EN_US] "已注册 {} 为环境 {}"
    registered-entity = [EN_US] "已注册 {} 为实体 {}"
    registered-enchantment = [EN_US] "已注册 {} 为附魔 {}"
    failed-register-enchantment = [EN_US] "注册附魔 {} 失败: {}"
    registered-potion = [EN_US] "已注册 {} 为药水 {}"
    failed-register-potion = [EN_US] "注册药水类型 {} 失败: {}"
    registered-block = [EN_US] "已注册 {} 为方块 {}"
    registered-item = [EN_US] "已注册 {} 为物品 {}"
    not-found-entity = [EN_US] "在 {} 中未找到 {}"
  }
  event-handler {
    registration-error = [EN_US] "注册事件处理器时发生错误: {} {}"
    invalid-signature = [EN_US] "{} 尝试注册无效的 EventHandler 方法签名 \"{}\" 在 {}"
    plugin-failed-register = [EN_US] "插件 {} 在尝试为 {} 注册事件时失败，因为 {} 不存在。"
  }
  entity-mapping {
    no-valid-mapping = [EN_US] "{} 没有有效的实体类映射"
  }
  plugin {
  }
}

# Server initialization messages
server-init {
  failed-initialize-server = [EN_US] "服务器初始化失败"
}



# Component bridge messages
component-bridge {
  initialized-successfully = [EN_US] "ComponentBridgeHandler 初始化成功，方法: {}"
  could-not-find-method = [EN_US] "在 Component 类中找不到 getSiblings 方法"
  failed-initialize = [EN_US] "ComponentBridgeHandler 初始化失败: {}"
}


# Server lifecycle messages
server {
  starting = [EN_US] "正在启动 Luminara 服务器..."
  started = [EN_US] "Luminara 服务器启动完成！耗时 {} 毫秒"
  stopping = [EN_US] "正在关闭 Luminara 服务器..."
  stopped = [EN_US] "Luminara 服务器已关闭"
  crash-report-saved = [EN_US] "Luminara 已将服务器崩溃日志保存到 {}，请注意！"
  crash-report-failed = [EN_US] "无法将服务器崩溃日志保存到磁盘，请检查磁盘空间和权限！"
  unexpected-exception = [EN_US] "遇到意外异常"
  overload-warning = [EN_US] "请检查服务器是否过载！已延迟 {} 毫秒或{} 个Tick！"
  exception-stopping = [EN_US] "关闭服务器时发生异常"
  async-world-save-starting = [EN_US] "服务器关闭期间开始异步世界保存..."
  async-world-save-starting-general = [EN_US] "开始异步世界保存..."
  async-world-save-failed = [EN_US] "保存世界 {} 失败"
  saving-chunks = [EN_US] "正在保存世界 '{}' 中的区块"
  minecraft-version = [EN_US] "正在启动 Minecraft 服务器，客户端版本为 {}"
  java-memory-too-low = [EN_US] "启动服务器分配的内存不足，请至少分配 1024MB 内存！参考启动参数: \"java -Xmx1024M -Xms1024M -jar server.jar\""
  loading-properties = [EN_US] "正在加载服务器配置文件 (server.properties)..."
  default-gamemode = [EN_US] "默认游戏模式: {}"
  starting-server = [EN_US] "正在 {}:{} 启动 Minecraft 服务器"
  player-auth-warning1 = [EN_US] "/// !!! Luminara 警告 !!! ///"
  player-auth-warning2 = [EN_US] "您的服务器正处于离线模式，将不会对玩家名称进行身份验证，请确保服务器安全！"
  player-auth-warning3 = [EN_US] "离线模式允许局域网玩家无需验证即可加入，但同时也存在安全风险！"
  player-auth-warning4 = [EN_US] "如需启用在线模式，请将 server.properties 中的 \"online-mode\" 设置为 \"true\"！"
  bind-port-warning1 = [EN_US] "/// !!! Luminara 警告 !!! ///"
  bind-port-warning2 = [EN_US] "发生意外情况: {}"
  bind-port-warning3 = [EN_US] "端口 {} 可能已被其他服务器占用！"
  start-done = [EN_US] "Luminara 启动成功！总耗时 {}，您可以使用 /help 命令查看帮助！"
}

# Optimization system messages
optimization {
  chunk {
    unloading = [EN_US] "正在卸载区块 [{}, {}] 在世界 {}"
    unloaded = [EN_US] "已卸载 {} 个区块"
    rate-limit = [EN_US] "区块加载速率限制: 原始 {} -> 限制 {}"
  }
  memory {
    cleanup-start = [EN_US] "开始内存清理..."
    cleanup-complete = [EN_US] "内存清理完成，释放了 {} MB"
    high-usage = [EN_US] "内存使用率过高: {}%"
    gc-triggered = [EN_US] "触发垃圾回收"
    cache-cleanup-completed = [EN_US] "Luminara 缓存清理完成"
    cache-cleanup-error = [EN_US] "缓存清理时发生错误"
    cache-cleanup-failed = [EN_US] "缓存清理失败"
  }
  manager {
    shutdown-error = [EN_US] "优化系统关闭时发生错误"
  }
  async-ai {
    calculation-error = [EN_US] "异步AI计算时发生错误"
    processing-error = [EN_US] "异步AI处理实体 {} 时发生错误"
    ai-calculation-error = [EN_US] "AI计算时发生错误"
  }
  async-collision {
    calculation-error = [EN_US] "异步碰撞计算时发生错误"
    processing-error = [EN_US] "异步碰撞处理实体 {} 时发生错误"
    check-error = [EN_US] "碰撞检查时发生错误"
    handling-error = [EN_US] "处理 {} 和 {} 之间的异步碰撞时发生错误"
    calculation-general-error = [EN_US] "碰撞计算时发生错误"
  }
  async-event {
    disabled-due-to-errors = [EN_US] "由于错误禁用事件类型的异步处理: {}"
    handler-error = [EN_US] "事件 {} 的异步事件处理器发生错误"
    registered = [EN_US] "已注册异步事件: {}"
    interrupted-shutdown = [EN_US] "关闭过程中被中断"
    registered-sync = [EN_US] "已注册同步事件: {}"
  }
}

# Velocity forwarding messages
velocity {
  enabled = [EN_US] "Velocity Modern 转发已启用"
  disabled = [EN_US] "Velocity Modern 转发已禁用"
  loaded-argument-types = [EN_US] "已加载 {} 个集成参数类型"
  failed-load-argument-types = [EN_US] "加载集成参数类型失败，使用默认值"
}



# World management messages
world {
  creating = [EN_US] "正在创建世界 {}"
  created = [EN_US] "世界 {} 已创建"
  loading = [EN_US] "正在加载世界 {}"
  loaded = [EN_US] "世界 {} 已加载"
  unloading = [EN_US] "正在卸载世界 {}"
  unloaded = [EN_US] "世界 {} 已卸载"
  saving = [EN_US] "正在保存世界: {}"
  saved-successfully = [EN_US] "世界 {} 保存成功"
  save-error = [EN_US] "保存世界 {} 时发生错误"
}

# Mod integration messages
mod {
  conflict-detected = [EN_US] "模组冲突检测: {} 与 {}"
  conflict-fatal = [EN_US] "致命模组冲突！这些模组不兼容，服务器将停止运行。请移除其中一个模组后重启服务器。"
}

# Sign block entity messages
sign {
  non-editable-warning = [EN_US] "玩家 {} 试图修改不可编辑的告示牌"
}

# Enum extender messages
enum {
  not-found-warning = [EN_US] "期望在 {} 中找到 {}，但未找到"
}

# World symlink messages
symlink {
  create-error = [EN_US] "创建符号链接时发生错误"
  file-exist = [EN_US] "创建符号链接 {} 时文件已存在"
  error-symlink = [EN_US] "文件系统不支持符号链接"
}



# Chat system messages
chat {
  message-too-long = [EN_US] "聊天消息过长！"
  empty-message-warning = [EN_US] "{} 试图发送空消息"
  long-message-warning = [EN_US] "{} 试图发送过长的消息: {} 个字符"
  illegal-characters = [EN_US] "聊天消息包含非法字符"
  player-removed = [EN_US] "玩家已被移除，无法发送消息"
}

# Player action messages
player {
  dropped-items-quickly = [EN_US] "{} 丢弃物品过快！"

  invalid-hotbar = [EN_US] "{} 试图设置无效的快捷栏选择"
  command-issued = [EN_US] "{} 执行了服务器命令: {}"
}

comments {
  _v.comment = [EN_US] [
  locale.comment = [EN_US] "语言/国际化相关设置"
  optimization {
    comment = [EN_US] "服务端优化相关设置"
    cache-plugin-class.comment = [EN_US] "是否缓存插件类以提高性能"
    goal-selector-update-interval.comment = [EN_US] [
    entity-optimization {
      comment = [EN_US] "实体优化相关设置"
      disable-entity-collisions.comment = [EN_US] "是否禁用实体碰撞检测"
      entity-cleanup-enabled.comment = [EN_US] "是否启用实体清理功能"
      entity-cleanup-threshold.comment = [EN_US] "触发实体清理的实体数量阈值"
      entity-freeze-timeout.comment = [EN_US] "实体冻结超时时间（毫秒）"
      reduce-entity-updates.comment = [EN_US] "是否减少实体更新频率"
      clean-valuable-items.comment = [EN_US] "是否清理有价值的物品"
      item-max-age.comment = [EN_US] "物品最大存在时间（tick）"
      cleanup-notification-enabled.comment = [EN_US] "是否启用清理通知"
      cleanup-warning-time.comment = [EN_US] "清理警告时间（秒）"
      cleanup-start-message.comment = [EN_US] "清理开始消息模板"
      cleanup-complete-message.comment = [EN_US] "清理完成消息模板"
      cleanup-cancelled-message.comment = [EN_US] "清理取消消息模板"
      entity-check-interval.comment = [EN_US] "实体检查间隔（tick）"
      entity-update-distance.comment = [EN_US] "实体更新距离"
      max-entities-per-chunk.comment = [EN_US] "每个区块最大实体数量"
      max-entities-per-type.comment = [EN_US] "每种类型最大实体数量"
      chunk-entity-limit.comment = [EN_US] "区块实体限制"
    }
    chunk-optimization {
      comment = [EN_US] "区块优化相关设置"
      aggressive-chunk-unloading.comment = [EN_US] "是否启用激进的区块卸载"
      chunk-unload-delay.comment = [EN_US] "区块卸载延迟（tick）"
      optimize-chunk-loading.comment = [EN_US] "是否优化区块加载"
      chunk-load-rate-limit.comment = [EN_US] "区块加载速率限制"
    }
    memory-optimization {
      comment = [EN_US] "内存优化相关设置"
      entity-cleanup-enabled.comment = [EN_US] "是否启用实体清理"
      cache-cleanup-enabled.comment = [EN_US] "是否启用缓存清理"
      cache-cleanup-interval.comment = [EN_US] "缓存清理间隔（秒）"
    }
    async-system {
      comment = [EN_US] "异步系统相关设置"
      enabled.comment = [EN_US] "是否启用异步系统"
      max-threads.comment = [EN_US] "最大线程数"
      async-ai-enabled.comment = [EN_US] "是否启用异步AI"
      async-collision-enabled.comment = [EN_US] "是否启用异步碰撞检测"
      async-redstone-enabled.comment = [EN_US] "是否启用异步红石"
      disable-on-error.comment = [EN_US] "出错时是否禁用异步系统"
      event-class-blacklist.comment = [EN_US] "事件类黑名单"
      mod-blacklist.comment = [EN_US] "模组黑名单"
      strict-class-checking.comment = [EN_US] "是否启用严格类检查"
      timeout-seconds.comment = [EN_US] "超时时间（秒）"
    }
    world-creation {
      comment = [EN_US] "世界创建相关设置"
      fast-world-creation.comment = [EN_US] "是否启用快速世界创建"
      skip-spawn-chunk-loading.comment = [EN_US] "是否跳过出生点区块加载"
      force-close-loading-screen.comment = [EN_US] "是否强制关闭加载屏幕"
      early-world-list-addition.comment = [EN_US] "是否提前添加到世界列表"
      parallel-world-initialization.comment = [EN_US] "是否并行初始化世界"
      world-init-timeout-seconds.comment = [EN_US] "世界初始化超时时间（秒）"
      max-concurrent-world-loads.comment = [EN_US] "最大并发世界加载数"
      optimize-world-border-setup.comment = [EN_US] "是否优化世界边界设置"
      defer-spawn-area-preparation.comment = [EN_US] "是否延迟出生区域准备"
      spawn-area-radius.comment = [EN_US] "出生区域半径"
      async-world-data-loading.comment = [EN_US] "是否异步加载世界数据"
    }
  }
  async-catcher.comment = [EN_US] [
  async-catcher.dump.comment = [EN_US] "是否在 debug 日志中打印堆栈信息"
  async-world-save.comment = [EN_US] [
  async-world-save.enabled.comment = [EN_US] "是否启用异步世界保存功能"
  async-world-save.timeout-seconds.comment = [EN_US] [
  async-world-save.save-world-data.comment = [EN_US] "是否在异步保存中包含世界数据"
  compatibility {
    symlink-world.comment = [EN_US] [
    extra-logic-worlds.comment = [EN_US] [
    forward-permission.comment = [EN_US] [
    valid-username-regex.comment = [EN_US] [
      "valid-username-regex = [EN_US] \".+\""
      "valid-username-regex = [EN_US] \".+\""
    lenient-item-tag-match.comment = [EN_US] [
  }
  velocity {
    comment = [EN_US] "Velocity Modern 转发相关设置"
    enabled.comment = [EN_US] [
    online-mode.comment = [EN_US] [
    forwarding-secret.comment = [EN_US] [
    debug-logging.comment = [EN_US] [
  }
}

# Missing but actually used keys
implementer {
  error = [EN_US] "实现器发生错误: {}"
}


