




mixin-load {
  core = [ES_ES] "核心 Mixin 配置已加载"
  optimization = [ES_ES] "优化 Mixin 配置已加载"
}


patcher {
  loading = [ES_ES] "正在加载 Plugin Patcher ..."
  loaded = [ES_ES] "加载了 {} 个 Patcher"
  load-error = [ES_ES] "加载 Patcher 时发生错误"
}
registry {
  forge-event = [ES_ES] "Luminara 事件系统已注册"
  begin = [ES_ES] "正在向 Bukkit 注册 ..."
  error = [ES_ES] "处理 Forge 注册时出错 "
  enchantment = [ES_ES] "注册了 {} 个新的附魔"
  potion = [ES_ES] "注册了 {} 个新的药水效果"
  material = [ES_ES] "注册了 {} 个材料，其中 {} 个方块 {} 个物品"
  entity-type = [ES_ES] "注册了 {} 个新的生物类型"
  environment = [ES_ES] "注册了 {} 个新的世界类型"
  villager-profession = [ES_ES] "注册了 {} 个新的村民职业"
  biome = [ES_ES] "注册了 {} 个新的生物群系"
  meta-type {
    error = [ES_ES] "{} 提供的 itemMetaType {} 无效: {}"
    no-candidate = [ES_ES] "{} 未在提供的 itemMetaType {} 找到合适的构造方法"
  }
  block-state {
    error = [ES_ES] "{} 提供的 itemMetaType {} 无效: {}"
    no-candidate = [ES_ES] "{} 未在提供的 blockStateClass {} 找到合适的构造方法"
  }
  entity {
    error = [ES_ES] "{} 提供的 entityClass {} 无效: {}"
  }
  debug {
    registered-cooking-category = [ES_ES] "已注册 {} 为烹饪类别 {}"
    registered-spawn-category = [ES_ES] "已注册 {} 为生成类别 {}"
    registered-custom-stats = [ES_ES] "已注册 {} 为自定义统计 {}"
    registered-biome = [ES_ES] "已注册 {} 为生物群系 {}"
    registered-environment = [ES_ES] "已注册 {} 为环境 {}"
    registered-entity = [ES_ES] "已注册 {} 为实体 {}"
    registered-enchantment = [ES_ES] "已注册 {} 为附魔 {}"
    failed-register-enchantment = [ES_ES] "注册附魔 {} 失败: {}"
    registered-potion = [ES_ES] "已注册 {} 为药水 {}"
    failed-register-potion = [ES_ES] "注册药水类型 {} 失败: {}"
    registered-block = [ES_ES] "已注册 {} 为方块 {}"
    registered-item = [ES_ES] "已注册 {} 为物品 {}"
    not-found-entity = [ES_ES] "在 {} 中未找到 {}"
  }
  event-handler {
    registration-error = [ES_ES] "注册事件处理器时发生错误: {} {}"
    invalid-signature = [ES_ES] "{} 尝试注册无效的 EventHandler 方法签名 \"{}\" 在 {}"
    plugin-failed-register = [ES_ES] "插件 {} 在尝试为 {} 注册事件时失败，因为 {} 不存在。"
  }
  entity-mapping {
    no-valid-mapping = [ES_ES] "{} 没有有效的实体类映射"
  }
  plugin {
  }
}

# Server initialization messages
server-init {
  failed-initialize-server = [ES_ES] "服务器初始化失败"
}



# Component bridge messages
component-bridge {
  initialized-successfully = [ES_ES] "ComponentBridgeHandler 初始化成功，方法: {}"
  could-not-find-method = [ES_ES] "在 Component 类中找不到 getSiblings 方法"
  failed-initialize = [ES_ES] "ComponentBridgeHandler 初始化失败: {}"
}


# Server lifecycle messages
server {
  starting = [ES_ES] "正在启动 Luminara 服务器..."
  started = [ES_ES] "Luminara 服务器启动完成！耗时 {} 毫秒"
  stopping = [ES_ES] "正在关闭 Luminara 服务器..."
  stopped = [ES_ES] "Luminara 服务器已关闭"
  crash-report-saved = [ES_ES] "Luminara 已将服务器崩溃日志保存到 {}，请注意！"
  crash-report-failed = [ES_ES] "无法将服务器崩溃日志保存到磁盘，请检查磁盘空间和权限！"
  unexpected-exception = [ES_ES] "遇到意外异常"
  overload-warning = [ES_ES] "请检查服务器是否过载！已延迟 {} 毫秒或{} 个Tick！"
  exception-stopping = [ES_ES] "关闭服务器时发生异常"
  async-world-save-starting = [ES_ES] "服务器关闭期间开始异步世界保存..."
  async-world-save-starting-general = [ES_ES] "开始异步世界保存..."
  async-world-save-failed = [ES_ES] "保存世界 {} 失败"
  saving-chunks = [ES_ES] "正在保存世界 '{}' 中的区块"
  minecraft-version = [ES_ES] "正在启动 Minecraft 服务器，客户端版本为 {}"
  java-memory-too-low = [ES_ES] "启动服务器分配的内存不足，请至少分配 1024MB 内存！参考启动参数: \"java -Xmx1024M -Xms1024M -jar server.jar\""
  loading-properties = [ES_ES] "正在加载服务器配置文件 (server.properties)..."
  default-gamemode = [ES_ES] "默认游戏模式: {}"
  starting-server = [ES_ES] "正在 {}:{} 启动 Minecraft 服务器"
  player-auth-warning1 = [ES_ES] "/// !!! Luminara 警告 !!! ///"
  player-auth-warning2 = [ES_ES] "您的服务器正处于离线模式，将不会对玩家名称进行身份验证，请确保服务器安全！"
  player-auth-warning3 = [ES_ES] "离线模式允许局域网玩家无需验证即可加入，但同时也存在安全风险！"
  player-auth-warning4 = [ES_ES] "如需启用在线模式，请将 server.properties 中的 \"online-mode\" 设置为 \"true\"！"
  bind-port-warning1 = [ES_ES] "/// !!! Luminara 警告 !!! ///"
  bind-port-warning2 = [ES_ES] "发生意外情况: {}"
  bind-port-warning3 = [ES_ES] "端口 {} 可能已被其他服务器占用！"
  start-done = [ES_ES] "Luminara 启动成功！总耗时 {}，您可以使用 /help 命令查看帮助！"
}

# Optimization system messages
optimization {
  chunk {
    unloading = [ES_ES] "正在卸载区块 [{}, {}] 在世界 {}"
    unloaded = [ES_ES] "已卸载 {} 个区块"
    rate-limit = [ES_ES] "区块加载速率限制: 原始 {} -> 限制 {}"
  }
  memory {
    cleanup-start = [ES_ES] "开始内存清理..."
    cleanup-complete = [ES_ES] "内存清理完成，释放了 {} MB"
    high-usage = [ES_ES] "内存使用率过高: {}%"
    gc-triggered = [ES_ES] "触发垃圾回收"
    cache-cleanup-completed = [ES_ES] "Luminara 缓存清理完成"
    cache-cleanup-error = [ES_ES] "缓存清理时发生错误"
    cache-cleanup-failed = [ES_ES] "缓存清理失败"
  }
  manager {
    shutdown-error = [ES_ES] "优化系统关闭时发生错误"
  }
  async-ai {
    calculation-error = [ES_ES] "异步AI计算时发生错误"
    processing-error = [ES_ES] "异步AI处理实体 {} 时发生错误"
    ai-calculation-error = [ES_ES] "AI计算时发生错误"
  }
  async-collision {
    calculation-error = [ES_ES] "异步碰撞计算时发生错误"
    processing-error = [ES_ES] "异步碰撞处理实体 {} 时发生错误"
    check-error = [ES_ES] "碰撞检查时发生错误"
    handling-error = [ES_ES] "处理 {} 和 {} 之间的异步碰撞时发生错误"
    calculation-general-error = [ES_ES] "碰撞计算时发生错误"
  }
  async-event {
    disabled-due-to-errors = [ES_ES] "由于错误禁用事件类型的异步处理: {}"
    handler-error = [ES_ES] "事件 {} 的异步事件处理器发生错误"
    registered = [ES_ES] "已注册异步事件: {}"
    interrupted-shutdown = [ES_ES] "关闭过程中被中断"
    registered-sync = [ES_ES] "已注册同步事件: {}"
  }
}

# Velocity forwarding messages
velocity {
  enabled = [ES_ES] "Velocity Modern 转发已启用"
  disabled = [ES_ES] "Velocity Modern 转发已禁用"
  loaded-argument-types = [ES_ES] "已加载 {} 个集成参数类型"
  failed-load-argument-types = [ES_ES] "加载集成参数类型失败，使用默认值"
}



# World management messages
world {
  creating = [ES_ES] "正在创建世界 {}"
  created = [ES_ES] "世界 {} 已创建"
  loading = [ES_ES] "正在加载世界 {}"
  loaded = [ES_ES] "世界 {} 已加载"
  unloading = [ES_ES] "正在卸载世界 {}"
  unloaded = [ES_ES] "世界 {} 已卸载"
  saving = [ES_ES] "正在保存世界: {}"
  saved-successfully = [ES_ES] "世界 {} 保存成功"
  save-error = [ES_ES] "保存世界 {} 时发生错误"
}

# Mod integration messages
mod {
  conflict-detected = [ES_ES] "模组冲突检测: {} 与 {}"
  conflict-fatal = [ES_ES] "致命模组冲突！这些模组不兼容，服务器将停止运行。请移除其中一个模组后重启服务器。"
}

# Sign block entity messages
sign {
  non-editable-warning = [ES_ES] "玩家 {} 试图修改不可编辑的告示牌"
}

# Enum extender messages
enum {
  not-found-warning = [ES_ES] "期望在 {} 中找到 {}，但未找到"
}

# World symlink messages
symlink {
  create-error = [ES_ES] "创建符号链接时发生错误"
  file-exist = [ES_ES] "创建符号链接 {} 时文件已存在"
  error-symlink = [ES_ES] "文件系统不支持符号链接"
}



# Chat system messages
chat {
  message-too-long = [ES_ES] "聊天消息过长！"
  empty-message-warning = [ES_ES] "{} 试图发送空消息"
  long-message-warning = [ES_ES] "{} 试图发送过长的消息: {} 个字符"
  illegal-characters = [ES_ES] "聊天消息包含非法字符"
  player-removed = [ES_ES] "玩家已被移除，无法发送消息"
}

# Player action messages
player {
  dropped-items-quickly = [ES_ES] "{} 丢弃物品过快！"

  invalid-hotbar = [ES_ES] "{} 试图设置无效的快捷栏选择"
  command-issued = [ES_ES] "{} 执行了服务器命令: {}"
}

comments {
  _v.comment = [ES_ES] [
  locale.comment = [ES_ES] "语言/国际化相关设置"
  optimization {
    comment = [ES_ES] "服务端优化相关设置"
    cache-plugin-class.comment = [ES_ES] "是否缓存插件类以提高性能"
    goal-selector-update-interval.comment = [ES_ES] [
    entity-optimization {
      comment = [ES_ES] "实体优化相关设置"
      disable-entity-collisions.comment = [ES_ES] "是否禁用实体碰撞检测"
      entity-cleanup-enabled.comment = [ES_ES] "是否启用实体清理功能"
      entity-cleanup-threshold.comment = [ES_ES] "触发实体清理的实体数量阈值"
      entity-freeze-timeout.comment = [ES_ES] "实体冻结超时时间（毫秒）"
      reduce-entity-updates.comment = [ES_ES] "是否减少实体更新频率"
      clean-valuable-items.comment = [ES_ES] "是否清理有价值的物品"
      item-max-age.comment = [ES_ES] "物品最大存在时间（tick）"
      cleanup-notification-enabled.comment = [ES_ES] "是否启用清理通知"
      cleanup-warning-time.comment = [ES_ES] "清理警告时间（秒）"
      cleanup-start-message.comment = [ES_ES] "清理开始消息模板"
      cleanup-complete-message.comment = [ES_ES] "清理完成消息模板"
      cleanup-cancelled-message.comment = [ES_ES] "清理取消消息模板"
      entity-check-interval.comment = [ES_ES] "实体检查间隔（tick）"
      entity-update-distance.comment = [ES_ES] "实体更新距离"
      max-entities-per-chunk.comment = [ES_ES] "每个区块最大实体数量"
      max-entities-per-type.comment = [ES_ES] "每种类型最大实体数量"
      chunk-entity-limit.comment = [ES_ES] "区块实体限制"
    }
    chunk-optimization {
      comment = [ES_ES] "区块优化相关设置"
      aggressive-chunk-unloading.comment = [ES_ES] "是否启用激进的区块卸载"
      chunk-unload-delay.comment = [ES_ES] "区块卸载延迟（tick）"
      optimize-chunk-loading.comment = [ES_ES] "是否优化区块加载"
      chunk-load-rate-limit.comment = [ES_ES] "区块加载速率限制"
    }
    memory-optimization {
      comment = [ES_ES] "内存优化相关设置"
      entity-cleanup-enabled.comment = [ES_ES] "是否启用实体清理"
      cache-cleanup-enabled.comment = [ES_ES] "是否启用缓存清理"
      cache-cleanup-interval.comment = [ES_ES] "缓存清理间隔（秒）"
    }
    async-system {
      comment = [ES_ES] "异步系统相关设置"
      enabled.comment = [ES_ES] "是否启用异步系统"
      max-threads.comment = [ES_ES] "最大线程数"
      async-ai-enabled.comment = [ES_ES] "是否启用异步AI"
      async-collision-enabled.comment = [ES_ES] "是否启用异步碰撞检测"
      async-redstone-enabled.comment = [ES_ES] "是否启用异步红石"
      disable-on-error.comment = [ES_ES] "出错时是否禁用异步系统"
      event-class-blacklist.comment = [ES_ES] "事件类黑名单"
      mod-blacklist.comment = [ES_ES] "模组黑名单"
      strict-class-checking.comment = [ES_ES] "是否启用严格类检查"
      timeout-seconds.comment = [ES_ES] "超时时间（秒）"
    }
    world-creation {
      comment = [ES_ES] "世界创建相关设置"
      fast-world-creation.comment = [ES_ES] "是否启用快速世界创建"
      skip-spawn-chunk-loading.comment = [ES_ES] "是否跳过出生点区块加载"
      force-close-loading-screen.comment = [ES_ES] "是否强制关闭加载屏幕"
      early-world-list-addition.comment = [ES_ES] "是否提前添加到世界列表"
      parallel-world-initialization.comment = [ES_ES] "是否并行初始化世界"
      world-init-timeout-seconds.comment = [ES_ES] "世界初始化超时时间（秒）"
      max-concurrent-world-loads.comment = [ES_ES] "最大并发世界加载数"
      optimize-world-border-setup.comment = [ES_ES] "是否优化世界边界设置"
      defer-spawn-area-preparation.comment = [ES_ES] "是否延迟出生区域准备"
      spawn-area-radius.comment = [ES_ES] "出生区域半径"
      async-world-data-loading.comment = [ES_ES] "是否异步加载世界数据"
    }
  }
  async-catcher.comment = [ES_ES] [
  async-catcher.dump.comment = [ES_ES] "是否在 debug 日志中打印堆栈信息"
  async-world-save.comment = [ES_ES] [
  async-world-save.enabled.comment = [ES_ES] "是否启用异步世界保存功能"
  async-world-save.timeout-seconds.comment = [ES_ES] [
  async-world-save.save-world-data.comment = [ES_ES] "是否在异步保存中包含世界数据"
  compatibility {
    symlink-world.comment = [ES_ES] [
    extra-logic-worlds.comment = [ES_ES] [
    forward-permission.comment = [ES_ES] [
    valid-username-regex.comment = [ES_ES] [
      "valid-username-regex = [ES_ES] \"^[ -~\\p{sc=Han}]{1,16}$\""
      "valid-username-regex = [ES_ES] \".+\""
    lenient-item-tag-match.comment = [ES_ES] [
  }
  velocity {
    comment = [ES_ES] "Velocity Modern 转发相关设置"
    enabled.comment = [ES_ES] [
    online-mode.comment = [ES_ES] [
    forwarding-secret.comment = [ES_ES] [
    debug-logging.comment = [ES_ES] [
  }
}

# Missing but actually used keys
implementer {
  error = [ES_ES] "实现器发生错误: {}"
}


