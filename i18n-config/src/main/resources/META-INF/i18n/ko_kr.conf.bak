




mixin-load {
  core = [KO_KR] "核心 Mixin 配置已加载"
  optimization = [KO_KR] "优化 Mixin 配置已加载"
}


patcher {
  loading = [KO_KR] "正在加载 Plugin Patcher ..."
  loaded = [KO_KR] "加载了 {} 个 Patcher"
  load-error = [KO_KR] "加载 Patcher 时发生错误"
}
registry {
  forge-event = [KO_KR] "Luminara 事件系统已注册"
  begin = [KO_KR] "正在向 Bukkit 注册 ..."
  error = [KO_KR] "处理 Forge 注册时出错 "
  enchantment = [KO_KR] "注册了 {} 个新的附魔"
  potion = [KO_KR] "注册了 {} 个新的药水效果"
  material = [KO_KR] "注册了 {} 个材料，其中 {} 个方块 {} 个物品"
  entity-type = [KO_KR] "注册了 {} 个新的生物类型"
  environment = [KO_KR] "注册了 {} 个新的世界类型"
  villager-profession = [KO_KR] "注册了 {} 个新的村民职业"
  biome = [KO_KR] "注册了 {} 个新的生物群系"
  meta-type {
    error = [KO_KR] "{} 提供的 itemMetaType {} 无效: {}"
    no-candidate = [KO_KR] "{} 未在提供的 itemMetaType {} 找到合适的构造方法"
  }
  block-state {
    error = [KO_KR] "{} 提供的 itemMetaType {} 无效: {}"
    no-candidate = [KO_KR] "{} 未在提供的 blockStateClass {} 找到合适的构造方法"
  }
  entity {
    error = [KO_KR] "{} 提供的 entityClass {} 无效: {}"
  }
  debug {
    registered-cooking-category = [KO_KR] "已注册 {} 为烹饪类别 {}"
    registered-spawn-category = [KO_KR] "已注册 {} 为生成类别 {}"
    registered-custom-stats = [KO_KR] "已注册 {} 为自定义统计 {}"
    registered-biome = [KO_KR] "已注册 {} 为生物群系 {}"
    registered-environment = [KO_KR] "已注册 {} 为环境 {}"
    registered-entity = [KO_KR] "已注册 {} 为实体 {}"
    registered-enchantment = [KO_KR] "已注册 {} 为附魔 {}"
    failed-register-enchantment = [KO_KR] "注册附魔 {} 失败: {}"
    registered-potion = [KO_KR] "已注册 {} 为药水 {}"
    failed-register-potion = [KO_KR] "注册药水类型 {} 失败: {}"
    registered-block = [KO_KR] "已注册 {} 为方块 {}"
    registered-item = [KO_KR] "已注册 {} 为物品 {}"
    not-found-entity = [KO_KR] "在 {} 中未找到 {}"
  }
  event-handler {
    registration-error = [KO_KR] "注册事件处理器时发生错误: {} {}"
    invalid-signature = [KO_KR] "{} 尝试注册无效的 EventHandler 方法签名 \"{}\" 在 {}"
    plugin-failed-register = [KO_KR] "插件 {} 在尝试为 {} 注册事件时失败，因为 {} 不存在。"
  }
  entity-mapping {
    no-valid-mapping = [KO_KR] "{} 没有有效的实体类映射"
  }
  plugin {
  }
}

# Server initialization messages
server-init {
  failed-initialize-server = [KO_KR] "服务器初始化失败"
}



# Component bridge messages
component-bridge {
  initialized-successfully = [KO_KR] "ComponentBridgeHandler 初始化成功，方法: {}"
  could-not-find-method = [KO_KR] "在 Component 类中找不到 getSiblings 方法"
  failed-initialize = [KO_KR] "ComponentBridgeHandler 初始化失败: {}"
}


# Server lifecycle messages
server {
  starting = [KO_KR] "正在启动 Luminara 服务器..."
  started = [KO_KR] "Luminara 服务器启动完成！耗时 {} 毫秒"
  stopping = [KO_KR] "正在关闭 Luminara 服务器..."
  stopped = [KO_KR] "Luminara 服务器已关闭"
  crash-report-saved = [KO_KR] "Luminara 已将服务器崩溃日志保存到 {}，请注意！"
  crash-report-failed = [KO_KR] "无法将服务器崩溃日志保存到磁盘，请检查磁盘空间和权限！"
  unexpected-exception = [KO_KR] "遇到意外异常"
  overload-warning = [KO_KR] "请检查服务器是否过载！已延迟 {} 毫秒或{} 个Tick！"
  exception-stopping = [KO_KR] "关闭服务器时发生异常"
  async-world-save-starting = [KO_KR] "服务器关闭期间开始异步世界保存..."
  async-world-save-starting-general = [KO_KR] "开始异步世界保存..."
  async-world-save-failed = [KO_KR] "保存世界 {} 失败"
  saving-chunks = [KO_KR] "正在保存世界 '{}' 中的区块"
  minecraft-version = [KO_KR] "正在启动 Minecraft 服务器，客户端版本为 {}"
  java-memory-too-low = [KO_KR] "启动服务器分配的内存不足，请至少分配 1024MB 内存！参考启动参数: \"java -Xmx1024M -Xms1024M -jar server.jar\""
  loading-properties = [KO_KR] "正在加载服务器配置文件 (server.properties)..."
  default-gamemode = [KO_KR] "默认游戏模式: {}"
  starting-server = [KO_KR] "正在 {}:{} 启动 Minecraft 服务器"
  player-auth-warning1 = [KO_KR] "/// !!! Luminara 警告 !!! ///"
  player-auth-warning2 = [KO_KR] "您的服务器正处于离线模式，将不会对玩家名称进行身份验证，请确保服务器安全！"
  player-auth-warning3 = [KO_KR] "离线模式允许局域网玩家无需验证即可加入，但同时也存在安全风险！"
  player-auth-warning4 = [KO_KR] "如需启用在线模式，请将 server.properties 中的 \"online-mode\" 设置为 \"true\"！"
  bind-port-warning1 = [KO_KR] "/// !!! Luminara 警告 !!! ///"
  bind-port-warning2 = [KO_KR] "发生意外情况: {}"
  bind-port-warning3 = [KO_KR] "端口 {} 可能已被其他服务器占用！"
  start-done = [KO_KR] "Luminara 启动成功！总耗时 {}，您可以使用 /help 命令查看帮助！"
}

# Optimization system messages
optimization {
  chunk {
    unloading = [KO_KR] "正在卸载区块 [{}, {}] 在世界 {}"
    unloaded = [KO_KR] "已卸载 {} 个区块"
    rate-limit = [KO_KR] "区块加载速率限制: 原始 {} -> 限制 {}"
  }
  memory {
    cleanup-start = [KO_KR] "开始内存清理..."
    cleanup-complete = [KO_KR] "内存清理完成，释放了 {} MB"
    high-usage = [KO_KR] "内存使用率过高: {}%"
    gc-triggered = [KO_KR] "触发垃圾回收"
    cache-cleanup-completed = [KO_KR] "Luminara 缓存清理完成"
    cache-cleanup-error = [KO_KR] "缓存清理时发生错误"
    cache-cleanup-failed = [KO_KR] "缓存清理失败"
  }
  manager {
    shutdown-error = [KO_KR] "优化系统关闭时发生错误"
  }
  async-ai {
    calculation-error = [KO_KR] "异步AI计算时发生错误"
    processing-error = [KO_KR] "异步AI处理实体 {} 时发生错误"
    ai-calculation-error = [KO_KR] "AI计算时发生错误"
  }
  async-collision {
    calculation-error = [KO_KR] "异步碰撞计算时发生错误"
    processing-error = [KO_KR] "异步碰撞处理实体 {} 时发生错误"
    check-error = [KO_KR] "碰撞检查时发生错误"
    handling-error = [KO_KR] "处理 {} 和 {} 之间的异步碰撞时发生错误"
    calculation-general-error = [KO_KR] "碰撞计算时发生错误"
  }
  async-event {
    disabled-due-to-errors = [KO_KR] "由于错误禁用事件类型的异步处理: {}"
    handler-error = [KO_KR] "事件 {} 的异步事件处理器发生错误"
    registered = [KO_KR] "已注册异步事件: {}"
    interrupted-shutdown = [KO_KR] "关闭过程中被中断"
    registered-sync = [KO_KR] "已注册同步事件: {}"
  }
}

# Velocity forwarding messages
velocity {
  enabled = [KO_KR] "Velocity Modern 转发已启用"
  disabled = [KO_KR] "Velocity Modern 转发已禁用"
  loaded-argument-types = [KO_KR] "已加载 {} 个集成参数类型"
  failed-load-argument-types = [KO_KR] "加载集成参数类型失败，使用默认值"
}



# World management messages
world {
  creating = [KO_KR] "正在创建世界 {}"
  created = [KO_KR] "世界 {} 已创建"
  loading = [KO_KR] "正在加载世界 {}"
  loaded = [KO_KR] "世界 {} 已加载"
  unloading = [KO_KR] "正在卸载世界 {}"
  unloaded = [KO_KR] "世界 {} 已卸载"
  saving = [KO_KR] "正在保存世界: {}"
  saved-successfully = [KO_KR] "世界 {} 保存成功"
  save-error = [KO_KR] "保存世界 {} 时发生错误"
}

# Mod integration messages
mod {
  conflict-detected = [KO_KR] "模组冲突检测: {} 与 {}"
  conflict-fatal = [KO_KR] "致命模组冲突！这些模组不兼容，服务器将停止运行。请移除其中一个模组后重启服务器。"
}

# Sign block entity messages
sign {
  non-editable-warning = [KO_KR] "玩家 {} 试图修改不可编辑的告示牌"
}

# Enum extender messages
enum {
  not-found-warning = [KO_KR] "期望在 {} 中找到 {}，但未找到"
}

# World symlink messages
symlink {
  create-error = [KO_KR] "创建符号链接时发生错误"
  file-exist = [KO_KR] "创建符号链接 {} 时文件已存在"
  error-symlink = [KO_KR] "文件系统不支持符号链接"
}



# Chat system messages
chat {
  message-too-long = [KO_KR] "聊天消息过长！"
  empty-message-warning = [KO_KR] "{} 试图发送空消息"
  long-message-warning = [KO_KR] "{} 试图发送过长的消息: {} 个字符"
  illegal-characters = [KO_KR] "聊天消息包含非法字符"
  player-removed = [KO_KR] "玩家已被移除，无法发送消息"
}

# Player action messages
player {
  dropped-items-quickly = [KO_KR] "{} 丢弃物品过快！"

  invalid-hotbar = [KO_KR] "{} 试图设置无效的快捷栏选择"
  command-issued = [KO_KR] "{} 执行了服务器命令: {}"
}

comments {
  _v.comment = [KO_KR] [
  locale.comment = [KO_KR] "语言/国际化相关设置"
  optimization {
    comment = [KO_KR] "服务端优化相关设置"
    cache-plugin-class.comment = [KO_KR] "是否缓存插件类以提高性能"
    goal-selector-update-interval.comment = [KO_KR] [
    entity-optimization {
      comment = [KO_KR] "实体优化相关设置"
      disable-entity-collisions.comment = [KO_KR] "是否禁用实体碰撞检测"
      entity-cleanup-enabled.comment = [KO_KR] "是否启用实体清理功能"
      entity-cleanup-threshold.comment = [KO_KR] "触发实体清理的实体数量阈值"
      entity-freeze-timeout.comment = [KO_KR] "实体冻结超时时间（毫秒）"
      reduce-entity-updates.comment = [KO_KR] "是否减少实体更新频率"
      clean-valuable-items.comment = [KO_KR] "是否清理有价值的物品"
      item-max-age.comment = [KO_KR] "物品最大存在时间（tick）"
      cleanup-notification-enabled.comment = [KO_KR] "是否启用清理通知"
      cleanup-warning-time.comment = [KO_KR] "清理警告时间（秒）"
      cleanup-start-message.comment = [KO_KR] "清理开始消息模板"
      cleanup-complete-message.comment = [KO_KR] "清理完成消息模板"
      cleanup-cancelled-message.comment = [KO_KR] "清理取消消息模板"
      entity-check-interval.comment = [KO_KR] "实体检查间隔（tick）"
      entity-update-distance.comment = [KO_KR] "实体更新距离"
      max-entities-per-chunk.comment = [KO_KR] "每个区块最大实体数量"
      max-entities-per-type.comment = [KO_KR] "每种类型最大实体数量"
      chunk-entity-limit.comment = [KO_KR] "区块实体限制"
    }
    chunk-optimization {
      comment = [KO_KR] "区块优化相关设置"
      aggressive-chunk-unloading.comment = [KO_KR] "是否启用激进的区块卸载"
      chunk-unload-delay.comment = [KO_KR] "区块卸载延迟（tick）"
      optimize-chunk-loading.comment = [KO_KR] "是否优化区块加载"
      chunk-load-rate-limit.comment = [KO_KR] "区块加载速率限制"
    }
    memory-optimization {
      comment = [KO_KR] "内存优化相关设置"
      entity-cleanup-enabled.comment = [KO_KR] "是否启用实体清理"
      cache-cleanup-enabled.comment = [KO_KR] "是否启用缓存清理"
      cache-cleanup-interval.comment = [KO_KR] "缓存清理间隔（秒）"
    }
    async-system {
      comment = [KO_KR] "异步系统相关设置"
      enabled.comment = [KO_KR] "是否启用异步系统"
      max-threads.comment = [KO_KR] "最大线程数"
      async-ai-enabled.comment = [KO_KR] "是否启用异步AI"
      async-collision-enabled.comment = [KO_KR] "是否启用异步碰撞检测"
      async-redstone-enabled.comment = [KO_KR] "是否启用异步红石"
      disable-on-error.comment = [KO_KR] "出错时是否禁用异步系统"
      event-class-blacklist.comment = [KO_KR] "事件类黑名单"
      mod-blacklist.comment = [KO_KR] "模组黑名单"
      strict-class-checking.comment = [KO_KR] "是否启用严格类检查"
      timeout-seconds.comment = [KO_KR] "超时时间（秒）"
    }
    world-creation {
      comment = [KO_KR] "世界创建相关设置"
      fast-world-creation.comment = [KO_KR] "是否启用快速世界创建"
      skip-spawn-chunk-loading.comment = [KO_KR] "是否跳过出生点区块加载"
      force-close-loading-screen.comment = [KO_KR] "是否强制关闭加载屏幕"
      early-world-list-addition.comment = [KO_KR] "是否提前添加到世界列表"
      parallel-world-initialization.comment = [KO_KR] "是否并行初始化世界"
      world-init-timeout-seconds.comment = [KO_KR] "世界初始化超时时间（秒）"
      max-concurrent-world-loads.comment = [KO_KR] "最大并发世界加载数"
      optimize-world-border-setup.comment = [KO_KR] "是否优化世界边界设置"
      defer-spawn-area-preparation.comment = [KO_KR] "是否延迟出生区域准备"
      spawn-area-radius.comment = [KO_KR] "出生区域半径"
      async-world-data-loading.comment = [KO_KR] "是否异步加载世界数据"
    }
  }
  async-catcher.comment = [KO_KR] [
  async-catcher.dump.comment = [KO_KR] "是否在 debug 日志中打印堆栈信息"
  async-world-save.comment = [KO_KR] [
  async-world-save.enabled.comment = [KO_KR] "是否启用异步世界保存功能"
  async-world-save.timeout-seconds.comment = [KO_KR] [
  async-world-save.save-world-data.comment = [KO_KR] "是否在异步保存中包含世界数据"
  compatibility {
    symlink-world.comment = [KO_KR] [
    extra-logic-worlds.comment = [KO_KR] [
    forward-permission.comment = [KO_KR] [
    valid-username-regex.comment = [KO_KR] [
      "valid-username-regex = [KO_KR] \"^[ -~\\p{sc=Han}]{1,16}$\""
      "valid-username-regex = [KO_KR] \".+\""
    lenient-item-tag-match.comment = [KO_KR] [
  }
  velocity {
    comment = [KO_KR] "Velocity Modern 转发相关设置"
    enabled.comment = [KO_KR] [
    online-mode.comment = [KO_KR] [
    forwarding-secret.comment = [KO_KR] [
    debug-logging.comment = [KO_KR] [
  }
}

# Missing but actually used keys
implementer {
  error = [KO_KR] "实现器发生错误: {}"
}


