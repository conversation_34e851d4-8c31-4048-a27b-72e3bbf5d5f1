java {
  deprecated = [
    "Вы используете устаревшую версию Java"
    "Текущая версия {0}, рекомендуемая {1}"
    "Поддержка используемой версии Java прекратится в дальнейшем"
  ]
}

Implementer {
  not-found = "Не найден класс {}"
  error = "Произошла ошибка {}"
}
i18n {
  current-not-available = "Текущая локализация {0} недоступна"
  using-language = "Применяется локализация {0} с откатом на {1}"
}
loading-mapping = "Загрузка разметки ..."
mixin-load {
  core = "Основной миксин Luminara добавлен."
  optimization = "Оптимизирующий миксин Luminara добавлен."
}
mod-load = "Luminara Mod загружен."
patcher {
  loading = "Загрузка патчей для плагинов ..."
  loaded = "Загружено {} патчеров"
  load-error = "Произошла ошибка при загрузке патчера"
}
registry {
  forge-event = "События Arclight зарегистрированы."
  begin = "Регистрирование для Bukkit ..."
  error = "Произошла ошибка регистрирования Forge "
  enchantment = "Зарегистрировано {} зачарований"
  potion = "Зарегистрировано {} новых эффектов зелий"
  material = "Зарегистрировано  {} новых материалов с {} блоками и {} предметами"
  entity-type = "Зарегистрировано {} новых типов существ"
  environment = "Зарегистрировано {} новых измерений"
  villager-profession = "Зарегистрировано {} новых профессий жителей"
  biome = "Зарегистрировано {} новых биомов"
  initialization-error = "Ошибка инициализации Arclight"
  meta-type {
  not-subclass = "{} не является подклассом {}"
  error = "{} предоставил некорректный itemMetaType {}: {}"
  no-candidate = "{} не обнаружил допустимый конструктор в предоставленном itemMetaType {}"
  }
  block-state {
  not-subclass = "{} не является подклассом {}"
  error = "{} предоставил некорректный itemMetaType {}: {}"
  no-candidate = "{} не обнаружил допустимый конструктор в предоставленном blockStateClass {}"
  }
  entity {
  not-subclass = "{} не является подклассом {}"
  error = "{} предоставил некорректный entityClass {}: {}"
  }
  debug {
    registered-cooking-category = "Зарегистрирован {} как категория готовки {}"
    registered-spawn-category = "Зарегистрирован {} как категория спавна {}"
    registered-custom-stats = "Зарегистрирован {} как пользовательская статистика {}"
    registered-biome = "Зарегистрирован {} как биом {}"
    registered-environment = "Зарегистрирован {} как окружение {}"
    registered-entity = "Зарегистрирован {} как сущность {}"
    registered-enchantment = "Зарегистрирован {} как зачарование {}"
    failed-register-enchantment = "Не удалось зарегистрировать зачарование {}: {}"
    registered-potion = "Зарегистрирован {} как зелье {}"
    failed-register-potion = "Не удалось зарегистрировать тип зелья {}: {}"
    registered-block = "Зарегистрирован {} как блок {}"
    registered-item = "Зарегистрирован {} как предмет {}"
    not-found-entity = "Не найден {} в {}"
  }
  event-handler {
    registration-error = "Ошибка регистрации обработчика событий: {} {}"
    invalid-signature = "{} попытался зарегистрировать недопустимую сигнатуру метода EventHandler \"{}\" в {}"
    plugin-failed-register = "Плагин {} не смог зарегистрировать события для {}, потому что {} не существует."
  }
  entity-mapping {
    no-valid-mapping = "{} не имеет действительного сопоставления класса сущности"
    missing-mapping-error = "Отсутствует действительное сопоставление класса сущности"
  }
  plugin {
    load-error-invalid-name = "Ошибка загрузки плагина '{}' (директория {}): недопустимое имя"
    load-error-space-in-name = "Ошибка загрузки плагина '{}' (директория {}): имя плагина содержит пробелы"
    load-error-general = "Ошибка загрузки плагина '{}' (директория {}): {}"
    load-error-simple = "Ошибка загрузки плагина '{}' (директория {})"
    enabling = "Включение плагина {}"
    disabling = "Отключение плагина {}"
  }
 }

# Server initialization messages
server-init {
  failed-initialize-server = "Не удалось инициализировать сервер"
}

# Class loading and cache messages
class-cache {
  obsolete-cleared = "Устаревший кэш классов плагинов очищен"
  failed-initialize = "Не удалось инициализировать кэш классов"
  failed-close = "Не удалось закрыть кэш классов"
  cannot-find-package = "Не удается найти пакет {}"
}

# Component bridge messages
component-bridge {
  initialized-successfully = "ComponentBridgeHandler успешно инициализирован с методом: {}"
  could-not-find-method = "Не удалось найти метод getSiblings в классе Component"
  failed-initialize = "Не удалось инициализировать ComponentBridgeHandler: {}"
}
error-symlink = "Файловая система не поддерживает символические ссылки"
symlink-file-exist = "Файл уже существует при создании символической ссылки {}"

# Server lifecycle messages
server {
  starting = "Запуск сервера Luminara..."
  started = "Запуск сервера Luminara завершен! Заняло {} миллисекунд"
  stopping = "Остановка сервера Luminara..."
  stopped = "Сервер Luminara остановлен"
  crash-report-saved = "Luminara сохранил отчет о сбое сервера в {}, пожалуйста, проверьте!"
  crash-report-failed = "Не удалось сохранить отчет о сбое сервера на диск, проверьте место на диске и разрешения!"
  unexpected-exception = "Обнаружено неожиданное исключение"
  overload-warning = "Не успевает! Сервер перегружен? Отстает на {} миллисекунд!"
  exception-stopping = "Исключение при остановке сервера"
  async-world-save-starting = "Начало асинхронного сохранения мира при выключении сервера..."
  async-world-save-starting-general = "Начало асинхронного сохранения мира..."
  async-world-save-failed = "Не удалось сохранить мир {}"
  saving-chunks = "Сохранение чанков мира '{}'"
  minecraft-version = "Запуск сервера Minecraft для клиентской версии {}"
  java-memory-too-low = "Недостаточно памяти для запуска сервера, выделите минимум 1024MB! Пример: \"java -Xmx1024M -Xms1024M -jar server.jar\""
  loading-properties = "Загрузка файла конфигурации сервера (server.properties)..."
  default-gamemode = "Режим игры по умолчанию: {}"
  starting-server = "Запуск сервера Minecraft на {}:{}"
  player-auth-warning1 = "/// !!! Предупреждение Luminara !!! ///"
  player-auth-warning2 = "Ваш сервер работает в автономном режиме и не будет аутентифицировать имена игроков, обеспечьте безопасность сервера!"
  player-auth-warning3 = "Автономный режим позволяет игрокам LAN присоединяться без аутентификации, но также создает риски безопасности!"
  player-auth-warning4 = "Чтобы включить онлайн-режим, установите \"online-mode\" в \"true\" в server.properties!"
  bind-port-warning1 = "/// !!! Предупреждение Luminara !!! ///"
  bind-port-warning2 = "Произошла неожиданная ситуация: {}"
  bind-port-warning3 = "Порт {} может уже использоваться другим сервером!"
  start-done = "Запуск Luminara завершен! Общее время: {}, используйте /help для справки по командам!"
}

# Optimization system messages
optimization {
  chunk {
    unloading = "Выгрузка чанка [{}, {}] в мире {}"
    unloaded = "{} чанков выгружено"
    rate-limit = "Ограничение скорости загрузки чанков: оригинал {} -> ограничено {}"
  }
  memory {
    cleanup-start = "Начинается очистка памяти..."
    cleanup-complete = "Очистка памяти завершена, освобождено {} МБ"
    high-usage = "Высокое использование памяти: {}%"
    gc-triggered = "Запущена сборка мусора"
    cache-cleanup-completed = "Очистка кэша Luminara завершена"
    cache-cleanup-error = "Ошибка при очистке кэша"
    cache-cleanup-failed = "Очистка кэша не удалась"
  }
  manager {
    shutdown-error = "Ошибка при выключении системы оптимизации"
  }
  async-ai {
    calculation-error = "Ошибка при асинхронном вычислении ИИ"
    processing-error = "Ошибка обработки сущности {} в асинхронном ИИ"
    ai-calculation-error = "Ошибка в вычислениях ИИ"
  }
  async-collision {
    calculation-error = "Ошибка при асинхронном вычислении столкновений"
    processing-error = "Ошибка обработки сущности {} в асинхронных столкновениях"
    check-error = "Ошибка при проверке столкновений"
    handling-error = "Ошибка обработки асинхронного столкновения между {} и {}"
    calculation-general-error = "Ошибка в вычислениях столкновений"
  }
  async-event {
    disabled-due-to-errors = "Асинхронная обработка отключена для типа события из-за ошибок: {}"
    handler-error = "Ошибка в асинхронном обработчике событий для события {}"
    registered = "Зарегистрировано асинхронное событие: {}"
    interrupted-shutdown = "Прервано во время выключения"
    registered-sync = "Зарегистрировано синхронное событие: {}"
  }
}

# Velocity forwarding messages
velocity {
  enabled = "Пересылка Velocity Modern включена"
  disabled = "Пересылка Velocity Modern отключена"
  loaded-argument-types = "Загружено {} типов аргументов интеграции"
  failed-load-argument-types = "Не удалось загрузить типы аргументов интеграции, используются значения по умолчанию"
}

# Error messages
error {
  class-not-found = "Класс не найден: {}"
  method-not-found = "Метод не найден: {}"
  field-not-found = "Поле не найдено: {}"
  invalid-configuration = "Недопустимый файл конфигурации: {}"
  file-not-found = "Файл не найден: {}"
  permission-denied = "Доступ запрещен: {}"
  network-error = "Сетевая ошибка: {}"
  database-error = "Ошибка базы данных: {}"
  plugin-error = "Ошибка плагина: {}"
  mixin-error = "Ошибка Mixin: {}"
}

# Warning messages
warning {
  deprecated-api = "Использование устаревшего API: {}"
  performance-issue = "Обнаружена проблема с производительностью: {}"
  memory-low = "Предупреждение о нехватке памяти, текущее использование: {}%"
  disk-space-low = "Мало места на диске: {} МБ осталось"
  plugin-conflict = "Обнаружен конфликт плагинов: {} может конфликтовать с {}"
  async-operation = "Предупреждение об асинхронной операции: {}"
}

# World management messages
world {
  creating = "Создание мира {}"
  created = "Мир {} создан"
  loading = "Загрузка мира {}"
  loaded = "Мир {} загружен"
  unloading = "Выгрузка мира {}"
  unloaded = "Мир {} выгружен"
  saving = "Сохранение мира: {}"
  saved-successfully = "Мир {} успешно сохранен"
  save-error = "Ошибка сохранения мира {}"
}

# Mod integration messages
mod {
  conflict-detected = "Обнаружен конфликт модов: {} против {}"
}

# Sign block entity messages
sign {
  non-editable-warning = "Игрок {} попытался изменить нередактируемую табличку"
}

# Enum extender messages
enum {
  not-found-warning = "Ожидалось найти {} в {}, но не найдено"
}

# World symlink messages
symlink {
  create-error = "Ошибка создания символической ссылки"
}

# Distribution validation messages
dist {
  logic-world-check = "Класс уровня {} рассматривается как логический мир: {}"
}

# Chat system messages
chat {
  message-too-long = "Сообщение чата слишком длинное!"
  empty-message-warning = "{} попытался отправить пустое сообщение"
  long-message-warning = "{} попытался отправить слишком длинное сообщение: {} символов"
  illegal-characters = "Сообщение чата содержит недопустимые символы"
  player-removed = "Игрок был удален, невозможно отправить сообщение"
}

# Player action messages
player {
  dropped-items-quickly = "{} слишком быстро выбросил предметы!"
  dropped-items-disconnect = "Вы слишком быстро выбросили предметы (читерство?)"
  invalid-hotbar = "{} попытался установить недопустимый выбор панели быстрого доступа"
  invalid-hotbar-disconnect = "Недопустимый выбор панели быстрого доступа (читерство?)"
  command-issued = "{} выполнил серверную команду: {}"
  internal-command-error = "Произошла внутренняя ошибка при выполнении этой команды"
  book-edited-quickly = "Книга редактируется слишком быстро!"
}

comments {
  _v.comment = [
    "Репозиторий: https://github.com/QianMoo0121/Luminara"
    "Баг-трекер: https://github.com/QianMoo0121/Luminara/issues"
    ""
    ""
    "Версия конфигурации, не исправляйте."
  ]
  locale.comment = "Язык/настройки I18n"
  optimization {
    comment = "Настройки серверных оптимизаций"
    goal-selector-update-interval.comment = [
      "Интервал в тиках для обновления селектора целей"
      "Более высокие значения потребляют меньше ресурсов"
      "Заставляют мобов реже менять свои цели"
    ]
  }
  async-catcher.comment = [
    «Настройки Async Catcher»
    «Есть четыре режима, BLOCK наиболее предпочтителен»
    «NONE - Ничего не предпринимать»
    «DISPATCH - Действовать в основном потоке без его блокировки»
    «BLOCK - Перехватывать основной поток и ожидать результат»
    «EXCEPTION - Выдавать исключение»
  ]
  async-catcher.dump.comment = "Сохранять трассировку стека в debug.log"
  async-world-save.comment = [
    "Настройки асинхронного сохранения мира"
    "Асинхронно сохранять данные мира при выключении сервера для сокращения времени выключения"
  ]
  async-world-save.enabled.comment = "Включить ли функцию асинхронного сохранения мира"
  async-world-save.timeout-seconds.comment = [
    "Тайм-аут асинхронного сохранения в секундах"
    "Если сохранение не завершится в течение этого времени, сервер продолжит процесс выключения"
  ]
  async-world-save.save-world-data.comment = "Включать ли данные мира в асинхронное сохранение"
  compatibility {
    symlink-world.comment = [
      "Создать символические ссылки на папку измерения мода, соответствующую имени мира Bukkit"
      "Включение этого может улучшить совместимость плагинов"
      "Изменение этого на продакшн сервере вызовет изменения в именах миров модов"
      "  и приведет к потере данных в плагинах, зависящих от имен миров"
      "См. https://github.com/IzzelAliz/Arclight/wiki/World-Symlink для подробностей"
    ]
    extra-logic-worlds.comment = [
      "Дополнительная логика работающих миров"
      "Если какие-то моды работают неправильно, попробуйте найти имена классов в логах, связанные с [EXT_LOGIC], и добавьте их сюда"
    ]
    forward-permission.comment = [
      "true - Перенаправлять запросы разрешений Forge в Bukkit"
      "false - Отключить перенаправление разрешений"
      "reverse - Перенаправлять запросы разрешений игроков Bukkit в Forge"
    ]
    valid-username-regex.comment = [
      "Регулярное выражение для проверки действительного имени пользователя. Оставьте пустым для использования ванильной проверки"
      "Следующее разрешает китайские символы:"
      "valid-username-regex = \"^[ -~\\\\p{sc=Han}]{1,16}$\""
      "Следующее разрешает любое имя пользователя для входа:"
      "valid-username-regex = \".+\""
    ]
    lenient-item-tag-match.comment = [
      "Позволяет предметам с пустым nbt тегом складываться с предметами без тега"
    ]
  }
  velocity {
    comment = "Настройки, связанные с Velocity Modern Forwarding"
    enabled.comment = [
      "Включить ли поддержку Velocity Modern Forwarding"
      "При включении позволяет интеграцию с прокси-сервером Velocity"
    ]
    online-mode.comment = [
      "Включить ли проверку онлайн-режима"
      "Обычно должно соответствовать настройке online-mode в конфигурации Velocity"
    ]
    forwarding-secret.comment = [
      "Секретный ключ пересылки Velocity"
      "Должен точно соответствовать forwarding-secret в файле конфигурации Velocity"
      "Используется для аутентификации запросов подключения от Velocity"
    ]
    debug-logging.comment = [
      "Отображать ли отладочную информацию, связанную с пересылкой Velocity, в логах"
      "Включите это для помощи в диагностике проблем с подключением"
    ]
  }
}

# Missing keys that are being used
server.bind-port-warning1 = "端口绑定警告"
server.player-auth-warning1 = "玩家身份验证警告"
error.class-not-found = "找不到类"
error.database-error = "数据库错误"
error.field-not-found = "找不到字段"
error.file-not-found = "找不到文件"
error.invalid-configuration = "配置无效"
error.method-not-found = "找不到方法"
error.mixin-error = "Mixin 错误"
error.network-error = "网络错误"
error.permission-denied = "权限被拒绝"
error.plugin-error = "插件错误"
warning.async-operation = "异步操作警告"
warning.deprecated-api = "已弃用的 API 警告"
warning.disk-space-low = "磁盘空间不足警告"
warning.memory-low = "内存不足警告"
warning.performance-issue = "性能问题警告"
warning.plugin-conflict = "插件冲突警告"
