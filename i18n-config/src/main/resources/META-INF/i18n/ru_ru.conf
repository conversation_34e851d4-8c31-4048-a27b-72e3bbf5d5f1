




mixin-load {
  core = [RU_RU] "核心 Mixin 配置已加载"
  optimization = [RU_RU] "优化 Mixin 配置已加载"
}


patcher {
  loading = [RU_RU] "正在加载 Plugin Patcher ..."
  loaded = [RU_RU] "加载了 {} 个 Patcher"
  load-error = [RU_RU] "加载 Patcher 时发生错误"
}
registry {
  forge-event = [RU_RU] "Luminara 事件系统已注册"
  begin = [RU_RU] "正在向 Bukkit 注册 ..."
  error = [RU_RU] "处理 Forge 注册时出错 "
  enchantment = [RU_RU] "注册了 {} 个新的附魔"
  potion = [RU_RU] "注册了 {} 个新的药水效果"
  material = [RU_RU] "注册了 {} 个材料，其中 {} 个方块 {} 个物品"
  entity-type = [RU_RU] "注册了 {} 个新的生物类型"
  environment = [RU_RU] "注册了 {} 个新的世界类型"
  villager-profession = [RU_RU] "注册了 {} 个新的村民职业"
  biome = [RU_RU] "注册了 {} 个新的生物群系"
  meta-type {
    error = [RU_RU] "{} 提供的 itemMetaType {} 无效: {}"
    no-candidate = [RU_RU] "{} 未在提供的 itemMetaType {} 找到合适的构造方法"
  }
  block-state {
    error = [RU_RU] "{} 提供的 itemMetaType {} 无效: {}"
    no-candidate = [RU_RU] "{} 未在提供的 blockStateClass {} 找到合适的构造方法"
  }
  entity {
    error = [RU_RU] "{} 提供的 entityClass {} 无效: {}"
  }
  debug {
    registered-cooking-category = [RU_RU] "已注册 {} 为烹饪类别 {}"
    registered-spawn-category = [RU_RU] "已注册 {} 为生成类别 {}"
    registered-custom-stats = [RU_RU] "已注册 {} 为自定义统计 {}"
    registered-biome = [RU_RU] "已注册 {} 为生物群系 {}"
    registered-environment = [RU_RU] "已注册 {} 为环境 {}"
    registered-entity = [RU_RU] "已注册 {} 为实体 {}"
    registered-enchantment = [RU_RU] "已注册 {} 为附魔 {}"
    failed-register-enchantment = [RU_RU] "注册附魔 {} 失败: {}"
    registered-potion = [RU_RU] "已注册 {} 为药水 {}"
    failed-register-potion = [RU_RU] "注册药水类型 {} 失败: {}"
    registered-block = [RU_RU] "已注册 {} 为方块 {}"
    registered-item = [RU_RU] "已注册 {} 为物品 {}"
    not-found-entity = [RU_RU] "在 {} 中未找到 {}"
  }
  event-handler {
    registration-error = [RU_RU] "注册事件处理器时发生错误: {} {}"
    invalid-signature = [RU_RU] "{} 尝试注册无效的 EventHandler 方法签名 \"{}\" 在 {}"
    plugin-failed-register = [RU_RU] "插件 {} 在尝试为 {} 注册事件时失败，因为 {} 不存在。"
  }
  entity-mapping {
    no-valid-mapping = [RU_RU] "{} 没有有效的实体类映射"
  }
  plugin {
  }
}

# Server initialization messages
server-init {
  failed-initialize-server = [RU_RU] "服务器初始化失败"
}



# Component bridge messages
component-bridge {
  initialized-successfully = [RU_RU] "ComponentBridgeHandler 初始化成功，方法: {}"
  could-not-find-method = [RU_RU] "在 Component 类中找不到 getSiblings 方法"
  failed-initialize = [RU_RU] "ComponentBridgeHandler 初始化失败: {}"
}


# Server lifecycle messages
server {
  starting = [RU_RU] "正在启动 Luminara 服务器..."
  started = [RU_RU] "Luminara 服务器启动完成！耗时 {} 毫秒"
  stopping = [RU_RU] "正在关闭 Luminara 服务器..."
  stopped = [RU_RU] "Luminara 服务器已关闭"
  crash-report-saved = [RU_RU] "Luminara 已将服务器崩溃日志保存到 {}，请注意！"
  crash-report-failed = [RU_RU] "无法将服务器崩溃日志保存到磁盘，请检查磁盘空间和权限！"
  unexpected-exception = [RU_RU] "遇到意外异常"
  overload-warning = [RU_RU] "请检查服务器是否过载！已延迟 {} 毫秒或{} 个Tick！"
  exception-stopping = [RU_RU] "关闭服务器时发生异常"
  async-world-save-starting = [RU_RU] "服务器关闭期间开始异步世界保存..."
  async-world-save-starting-general = [RU_RU] "开始异步世界保存..."
  async-world-save-failed = [RU_RU] "保存世界 {} 失败"
  saving-chunks = [RU_RU] "正在保存世界 '{}' 中的区块"
  minecraft-version = [RU_RU] "正在启动 Minecraft 服务器，客户端版本为 {}"
  java-memory-too-low = [RU_RU] "启动服务器分配的内存不足，请至少分配 1024MB 内存！参考启动参数: \"java -Xmx1024M -Xms1024M -jar server.jar\""
  loading-properties = [RU_RU] "正在加载服务器配置文件 (server.properties)..."
  default-gamemode = [RU_RU] "默认游戏模式: {}"
  starting-server = [RU_RU] "正在 {}:{} 启动 Minecraft 服务器"
  player-auth-warning1 = [RU_RU] "/// !!! Luminara 警告 !!! ///"
  player-auth-warning2 = [RU_RU] "您的服务器正处于离线模式，将不会对玩家名称进行身份验证，请确保服务器安全！"
  player-auth-warning3 = [RU_RU] "离线模式允许局域网玩家无需验证即可加入，但同时也存在安全风险！"
  player-auth-warning4 = [RU_RU] "如需启用在线模式，请将 server.properties 中的 \"online-mode\" 设置为 \"true\"！"
  bind-port-warning1 = [RU_RU] "/// !!! Luminara 警告 !!! ///"
  bind-port-warning2 = [RU_RU] "发生意外情况: {}"
  bind-port-warning3 = [RU_RU] "端口 {} 可能已被其他服务器占用！"
  start-done = [RU_RU] "Luminara 启动成功！总耗时 {}，您可以使用 /help 命令查看帮助！"
}

# Optimization system messages
optimization {
  chunk {
    unloading = [RU_RU] "正在卸载区块 [{}, {}] 在世界 {}"
    unloaded = [RU_RU] "已卸载 {} 个区块"
    rate-limit = [RU_RU] "区块加载速率限制: 原始 {} -> 限制 {}"
  }
  memory {
    cleanup-start = [RU_RU] "开始内存清理..."
    cleanup-complete = [RU_RU] "内存清理完成，释放了 {} MB"
    high-usage = [RU_RU] "内存使用率过高: {}%"
    gc-triggered = [RU_RU] "触发垃圾回收"
    cache-cleanup-completed = [RU_RU] "Luminara 缓存清理完成"
    cache-cleanup-error = [RU_RU] "缓存清理时发生错误"
    cache-cleanup-failed = [RU_RU] "缓存清理失败"
  }
  manager {
    shutdown-error = [RU_RU] "优化系统关闭时发生错误"
  }
  async-ai {
    calculation-error = [RU_RU] "异步AI计算时发生错误"
    processing-error = [RU_RU] "异步AI处理实体 {} 时发生错误"
    ai-calculation-error = [RU_RU] "AI计算时发生错误"
  }
  async-collision {
    calculation-error = [RU_RU] "异步碰撞计算时发生错误"
    processing-error = [RU_RU] "异步碰撞处理实体 {} 时发生错误"
    check-error = [RU_RU] "碰撞检查时发生错误"
    handling-error = [RU_RU] "处理 {} 和 {} 之间的异步碰撞时发生错误"
    calculation-general-error = [RU_RU] "碰撞计算时发生错误"
  }
  async-event {
    disabled-due-to-errors = [RU_RU] "由于错误禁用事件类型的异步处理: {}"
    handler-error = [RU_RU] "事件 {} 的异步事件处理器发生错误"
    registered = [RU_RU] "已注册异步事件: {}"
    interrupted-shutdown = [RU_RU] "关闭过程中被中断"
    registered-sync = [RU_RU] "已注册同步事件: {}"
  }
}

# Velocity forwarding messages
velocity {
  enabled = [RU_RU] "Velocity Modern 转发已启用"
  disabled = [RU_RU] "Velocity Modern 转发已禁用"
  loaded-argument-types = [RU_RU] "已加载 {} 个集成参数类型"
  failed-load-argument-types = [RU_RU] "加载集成参数类型失败，使用默认值"
}



# World management messages
world {
  creating = [RU_RU] "正在创建世界 {}"
  created = [RU_RU] "世界 {} 已创建"
  loading = [RU_RU] "正在加载世界 {}"
  loaded = [RU_RU] "世界 {} 已加载"
  unloading = [RU_RU] "正在卸载世界 {}"
  unloaded = [RU_RU] "世界 {} 已卸载"
  saving = [RU_RU] "正在保存世界: {}"
  saved-successfully = [RU_RU] "世界 {} 保存成功"
  save-error = [RU_RU] "保存世界 {} 时发生错误"
}

# Mod integration messages
mod {
  conflict-detected = [RU_RU] "模组冲突检测: {} 与 {}"
  conflict-fatal = [RU_RU] "致命模组冲突！这些模组不兼容，服务器将停止运行。请移除其中一个模组后重启服务器。"
}

# Sign block entity messages
sign {
  non-editable-warning = [RU_RU] "玩家 {} 试图修改不可编辑的告示牌"
}

# Enum extender messages
enum {
  not-found-warning = [RU_RU] "期望在 {} 中找到 {}，但未找到"
}

# World symlink messages
symlink {
  create-error = [RU_RU] "创建符号链接时发生错误"
  file-exist = [RU_RU] "创建符号链接 {} 时文件已存在"
  error-symlink = [RU_RU] "文件系统不支持符号链接"
}



# Chat system messages
chat {
  message-too-long = [RU_RU] "聊天消息过长！"
  empty-message-warning = [RU_RU] "{} 试图发送空消息"
  long-message-warning = [RU_RU] "{} 试图发送过长的消息: {} 个字符"
  illegal-characters = [RU_RU] "聊天消息包含非法字符"
  player-removed = [RU_RU] "玩家已被移除，无法发送消息"
}

# Player action messages
player {
  dropped-items-quickly = [RU_RU] "{} 丢弃物品过快！"

  invalid-hotbar = [RU_RU] "{} 试图设置无效的快捷栏选择"
  command-issued = [RU_RU] "{} 执行了服务器命令: {}"
}

comments {
  _v.comment = [RU_RU] [
  locale.comment = [RU_RU] "语言/国际化相关设置"
  optimization {
    comment = [RU_RU] "服务端优化相关设置"
    cache-plugin-class.comment = [RU_RU] "是否缓存插件类以提高性能"
    goal-selector-update-interval.comment = [RU_RU] [
    entity-optimization {
      comment = [RU_RU] "实体优化相关设置"
      disable-entity-collisions.comment = [RU_RU] "是否禁用实体碰撞检测"
      entity-cleanup-enabled.comment = [RU_RU] "是否启用实体清理功能"
      entity-cleanup-threshold.comment = [RU_RU] "触发实体清理的实体数量阈值"
      entity-freeze-timeout.comment = [RU_RU] "实体冻结超时时间（毫秒）"
      reduce-entity-updates.comment = [RU_RU] "是否减少实体更新频率"
      clean-valuable-items.comment = [RU_RU] "是否清理有价值的物品"
      item-max-age.comment = [RU_RU] "物品最大存在时间（tick）"
      cleanup-notification-enabled.comment = [RU_RU] "是否启用清理通知"
      cleanup-warning-time.comment = [RU_RU] "清理警告时间（秒）"
      cleanup-start-message.comment = [RU_RU] "清理开始消息模板"
      cleanup-complete-message.comment = [RU_RU] "清理完成消息模板"
      cleanup-cancelled-message.comment = [RU_RU] "清理取消消息模板"
      entity-check-interval.comment = [RU_RU] "实体检查间隔（tick）"
      entity-update-distance.comment = [RU_RU] "实体更新距离"
      max-entities-per-chunk.comment = [RU_RU] "每个区块最大实体数量"
      max-entities-per-type.comment = [RU_RU] "每种类型最大实体数量"
      chunk-entity-limit.comment = [RU_RU] "区块实体限制"
    }
    chunk-optimization {
      comment = [RU_RU] "区块优化相关设置"
      aggressive-chunk-unloading.comment = [RU_RU] "是否启用激进的区块卸载"
      chunk-unload-delay.comment = [RU_RU] "区块卸载延迟（tick）"
      optimize-chunk-loading.comment = [RU_RU] "是否优化区块加载"
      chunk-load-rate-limit.comment = [RU_RU] "区块加载速率限制"
    }
    memory-optimization {
      comment = [RU_RU] "内存优化相关设置"
      entity-cleanup-enabled.comment = [RU_RU] "是否启用实体清理"
      cache-cleanup-enabled.comment = [RU_RU] "是否启用缓存清理"
      cache-cleanup-interval.comment = [RU_RU] "缓存清理间隔（秒）"
    }
    async-system {
      comment = [RU_RU] "异步系统相关设置"
      enabled.comment = [RU_RU] "是否启用异步系统"
      max-threads.comment = [RU_RU] "最大线程数"
      async-ai-enabled.comment = [RU_RU] "是否启用异步AI"
      async-collision-enabled.comment = [RU_RU] "是否启用异步碰撞检测"
      async-redstone-enabled.comment = [RU_RU] "是否启用异步红石"
      disable-on-error.comment = [RU_RU] "出错时是否禁用异步系统"
      event-class-blacklist.comment = [RU_RU] "事件类黑名单"
      mod-blacklist.comment = [RU_RU] "模组黑名单"
      strict-class-checking.comment = [RU_RU] "是否启用严格类检查"
      timeout-seconds.comment = [RU_RU] "超时时间（秒）"
    }
    world-creation {
      comment = [RU_RU] "世界创建相关设置"
      fast-world-creation.comment = [RU_RU] "是否启用快速世界创建"
      skip-spawn-chunk-loading.comment = [RU_RU] "是否跳过出生点区块加载"
      force-close-loading-screen.comment = [RU_RU] "是否强制关闭加载屏幕"
      early-world-list-addition.comment = [RU_RU] "是否提前添加到世界列表"
      parallel-world-initialization.comment = [RU_RU] "是否并行初始化世界"
      world-init-timeout-seconds.comment = [RU_RU] "世界初始化超时时间（秒）"
      max-concurrent-world-loads.comment = [RU_RU] "最大并发世界加载数"
      optimize-world-border-setup.comment = [RU_RU] "是否优化世界边界设置"
      defer-spawn-area-preparation.comment = [RU_RU] "是否延迟出生区域准备"
      spawn-area-radius.comment = [RU_RU] "出生区域半径"
      async-world-data-loading.comment = [RU_RU] "是否异步加载世界数据"
    }
  }
  async-catcher.comment = [RU_RU] [
  async-catcher.dump.comment = [RU_RU] "是否在 debug 日志中打印堆栈信息"
  async-world-save.comment = [RU_RU] [
  async-world-save.enabled.comment = [RU_RU] "是否启用异步世界保存功能"
  async-world-save.timeout-seconds.comment = [RU_RU] [
  async-world-save.save-world-data.comment = [RU_RU] "是否在异步保存中包含世界数据"
  compatibility {
    symlink-world.comment = [RU_RU] [
    extra-logic-worlds.comment = [RU_RU] [
    forward-permission.comment = [RU_RU] [
    valid-username-regex.comment = [RU_RU] [
      "valid-username-regex = [RU_RU] \".+\""
      "valid-username-regex = [RU_RU] \".+\""
    lenient-item-tag-match.comment = [RU_RU] [
  }
  velocity {
    comment = [RU_RU] "Velocity Modern 转发相关设置"
    enabled.comment = [RU_RU] [
    online-mode.comment = [RU_RU] [
    forwarding-secret.comment = [RU_RU] [
    debug-logging.comment = [RU_RU] [
  }
}

# Missing but actually used keys
implementer {
  error = [RU_RU] "实现器发生错误: {}"
}


