




mixin-load {
  core = "核心 Mixin 配置已加载"
  optimization = "优化 Mixin 配置已加载"
}


patcher {
  loading = "正在加载 Plugin Patcher ..."
  loaded = "加载了 {} 个 Patcher"
  load-error = "加载 Patcher 时发生错误"
}
registry {
  forge-event = "Luminara 事件系统已注册"
  begin = "正在向 Bukkit 注册 ..."
  error = "处理 Forge 注册时出错 "
  enchantment = "注册了 {} 个新的附魔"
  potion = "注册了 {} 个新的药水效果"
  material = "注册了 {} 个材料，其中 {} 个方块 {} 个物品"
  entity-type = "注册了 {} 个新的生物类型"
  environment = "注册了 {} 个新的世界类型"
  villager-profession = "注册了 {} 个新的村民职业"
  biome = "注册了 {} 个新的生物群系"
  meta-type {
    error = "{} 提供的 itemMetaType {} 无效: {}"
    no-candidate = "{} 未在提供的 itemMetaType {} 找到合适的构造方法"
  }
  block-state {
    error = "{} 提供的 itemMetaType {} 无效: {}"
    no-candidate = "{} 未在提供的 blockStateClass {} 找到合适的构造方法"
  }
  entity {
    error = "{} 提供的 entityClass {} 无效: {}"
  }
  debug {
    registered-cooking-category = "已注册 {} 为烹饪类别 {}"
    registered-spawn-category = "已注册 {} 为生成类别 {}"
    registered-custom-stats = "已注册 {} 为自定义统计 {}"
    registered-biome = "已注册 {} 为生物群系 {}"
    registered-environment = "已注册 {} 为环境 {}"
    registered-entity = "已注册 {} 为实体 {}"
    registered-enchantment = "已注册 {} 为附魔 {}"
    failed-register-enchantment = "注册附魔 {} 失败: {}"
    registered-potion = "已注册 {} 为药水 {}"
    failed-register-potion = "注册药水类型 {} 失败: {}"
    registered-block = "已注册 {} 为方块 {}"
    registered-item = "已注册 {} 为物品 {}"
    not-found-entity = "在 {} 中未找到 {}"
  }
  event-handler {
    registration-error = "注册事件处理器时发生错误: {} {}"
    invalid-signature = "{} 尝试注册无效的 EventHandler 方法签名 \"{}\" 在 {}"
    plugin-failed-register = "插件 {} 在尝试为 {} 注册事件时失败，因为 {} 不存在。"
  }
  entity-mapping {
    no-valid-mapping = "{} 没有有效的实体类映射"
  }
  plugin {
  }
}

# Server initialization messages
server-init {
  failed-initialize-server = "服务器初始化失败"
}



# Component bridge messages
component-bridge {
  initialized-successfully = "ComponentBridgeHandler 初始化成功，方法: {}"
  could-not-find-method = "在 Component 类中找不到 getSiblings 方法"
  failed-initialize = "ComponentBridgeHandler 初始化失败: {}"
}


# Server lifecycle messages
server {
  starting = "正在启动 Luminara 服务器..."
  started = "Luminara 服务器启动完成！耗时 {} 毫秒"
  stopping = "正在关闭 Luminara 服务器..."
  stopped = "Luminara 服务器已关闭"
  crash-report-saved = "Luminara 已将服务器崩溃日志保存到 {}，请注意！"
  crash-report-failed = "无法将服务器崩溃日志保存到磁盘，请检查磁盘空间和权限！"
  unexpected-exception = "遇到意外异常"
  overload-warning = "请检查服务器是否过载！已延迟 {} 毫秒或{} 个Tick！"
  exception-stopping = "关闭服务器时发生异常"
  async-world-save-starting = "服务器关闭期间开始异步世界保存..."
  async-world-save-starting-general = "开始异步世界保存..."
  async-world-save-failed = "保存世界 {} 失败"
  saving-chunks = "正在保存世界 '{}' 中的区块"
  minecraft-version = "正在启动 Minecraft 服务器，客户端版本为 {}"
  java-memory-too-low = "启动服务器分配的内存不足，请至少分配 1024MB 内存！参考启动参数: \"java -Xmx1024M -Xms1024M -jar server.jar\""
  loading-properties = "正在加载服务器配置文件 (server.properties)..."
  default-gamemode = "默认游戏模式: {}"
  starting-server = "正在 {}:{} 启动 Minecraft 服务器"
  player-auth-warning1 = "/// !!! Luminara 警告 !!! ///"
  player-auth-warning2 = "您的服务器正处于离线模式，将不会对玩家名称进行身份验证，请确保服务器安全！"
  player-auth-warning3 = "离线模式允许局域网玩家无需验证即可加入，但同时也存在安全风险！"
  player-auth-warning4 = "如需启用在线模式，请将 server.properties 中的 \"online-mode\" 设置为 \"true\"！"
  bind-port-warning1 = "/// !!! Luminara 警告 !!! ///"
  bind-port-warning2 = "发生意外情况: {}"
  bind-port-warning3 = "端口 {} 可能已被其他服务器占用！"
  start-done = "Luminara 启动成功！总耗时 {}，您可以使用 /help 命令查看帮助！"
}

# Optimization system messages
optimization {
  chunk {
    unloading = "正在卸载区块 [{}, {}] 在世界 {}"
    unloaded = "已卸载 {} 个区块"
    rate-limit = "区块加载速率限制: 原始 {} -> 限制 {}"
  }
  memory {
    cleanup-start = "开始内存清理..."
    cleanup-complete = "内存清理完成，释放了 {} MB"
    high-usage = "内存使用率过高: {}%"
    gc-triggered = "触发垃圾回收"
    cache-cleanup-completed = "Luminara 缓存清理完成"
    cache-cleanup-error = "缓存清理时发生错误"
    cache-cleanup-failed = "缓存清理失败"
  }
  manager {
    shutdown-error = "优化系统关闭时发生错误"
  }
  async-ai {
    calculation-error = "异步AI计算时发生错误"
    processing-error = "异步AI处理实体 {} 时发生错误"
    ai-calculation-error = "AI计算时发生错误"
  }
  async-collision {
    calculation-error = "异步碰撞计算时发生错误"
    processing-error = "异步碰撞处理实体 {} 时发生错误"
    check-error = "碰撞检查时发生错误"
    handling-error = "处理 {} 和 {} 之间的异步碰撞时发生错误"
    calculation-general-error = "碰撞计算时发生错误"
  }
  async-event {
    disabled-due-to-errors = "由于错误禁用事件类型的异步处理: {}"
    handler-error = "事件 {} 的异步事件处理器发生错误"
    registered = "已注册异步事件: {}"
    interrupted-shutdown = "关闭过程中被中断"
    registered-sync = "已注册同步事件: {}"
  }
}

# Velocity forwarding messages
velocity {
  enabled = "Velocity Modern 转发已启用"
  disabled = "Velocity Modern 转发已禁用"
  loaded-argument-types = "已加载 {} 个集成参数类型"
  failed-load-argument-types = "加载集成参数类型失败，使用默认值"
}



# World management messages
world {
  creating = "正在创建世界 {}"
  created = "世界 {} 已创建"
  loading = "正在加载世界 {}"
  loaded = "世界 {} 已加载"
  unloading = "正在卸载世界 {}"
  unloaded = "世界 {} 已卸载"
  saving = "正在保存世界: {}"
  saved-successfully = "世界 {} 保存成功"
  save-error = "保存世界 {} 时发生错误"
}

# Mod integration messages
mod {
  conflict-detected = "模组冲突检测: {} 与 {}"
  conflict-fatal = "致命模组冲突！这些模组不兼容，服务器将停止运行。请移除其中一个模组后重启服务器。"
}

# Sign block entity messages
sign {
  non-editable-warning = "玩家 {} 试图修改不可编辑的告示牌"
}

# Enum extender messages
enum {
  not-found-warning = "期望在 {} 中找到 {}，但未找到"
}

# World symlink messages
symlink {
  create-error = "创建符号链接时发生错误"
  file-exist = "创建符号链接 {} 时文件已存在"
  error-symlink = "文件系统不支持符号链接"
}



# Chat system messages
chat {
  message-too-long = "聊天消息过长！"
  empty-message-warning = "{} 试图发送空消息"
  long-message-warning = "{} 试图发送过长的消息: {} 个字符"
  illegal-characters = "聊天消息包含非法字符"
  player-removed = "玩家已被移除，无法发送消息"
}

# Player action messages
player {
  dropped-items-quickly = "{} 丢弃物品过快！"

  invalid-hotbar = "{} 试图设置无效的快捷栏选择"
  command-issued = "{} 执行了服务器命令: {}"
}

comments {
  _v.comment = [
    "源代码仓库: https://github.com/QianMoo0121/Luminara"
    "提交反馈/错误报告: https://github.com/QianMoo0121/Luminara/issues"
    ""
    ""
    "配置文件版本号，请勿编辑"
  ]
  locale.comment = "语言/国际化相关设置"
  optimization {
    comment = "服务端优化相关设置"
    cache-plugin-class.comment = "是否缓存插件类以提高性能"
    goal-selector-update-interval.comment = [
      "实体目标选择器的更新间隔"
      "数值越高消耗资源越少"
      "导致实体更改目标速度变慢"
    ]
    entity-optimization {
      comment = "实体优化相关设置"
      disable-entity-collisions.comment = "是否禁用实体碰撞检测"
      entity-cleanup-enabled.comment = "是否启用实体清理功能"
      entity-cleanup-threshold.comment = "触发实体清理的实体数量阈值"
      entity-freeze-timeout.comment = "实体冻结超时时间（毫秒）"
      reduce-entity-updates.comment = "是否减少实体更新频率"
      clean-valuable-items.comment = "是否清理有价值的物品"
      item-max-age.comment = "物品最大存在时间（tick）"
      cleanup-notification-enabled.comment = "是否启用清理通知"
      cleanup-warning-time.comment = "清理警告时间（秒）"
      cleanup-start-message.comment = "清理开始消息模板"
      cleanup-complete-message.comment = "清理完成消息模板"
      cleanup-cancelled-message.comment = "清理取消消息模板"
      entity-check-interval.comment = "实体检查间隔（tick）"
      entity-update-distance.comment = "实体更新距离"
      max-entities-per-chunk.comment = "每个区块最大实体数量"
      max-entities-per-type.comment = "每种类型最大实体数量"
      chunk-entity-limit.comment = "区块实体限制"
    }
    chunk-optimization {
      comment = "区块优化相关设置"
      aggressive-chunk-unloading.comment = "是否启用激进的区块卸载"
      chunk-unload-delay.comment = "区块卸载延迟（tick）"
      optimize-chunk-loading.comment = "是否优化区块加载"
      chunk-load-rate-limit.comment = "区块加载速率限制"
    }
    memory-optimization {
      comment = "内存优化相关设置"
      entity-cleanup-enabled.comment = "是否启用实体清理"
      cache-cleanup-enabled.comment = "是否启用缓存清理"
      cache-cleanup-interval.comment = "缓存清理间隔（秒）"
    }
    async-system {
      comment = "异步系统相关设置"
      enabled.comment = "是否启用异步系统"
      max-threads.comment = "最大线程数"
      async-ai-enabled.comment = "是否启用异步AI"
      async-collision-enabled.comment = "是否启用异步碰撞检测"
      async-redstone-enabled.comment = "是否启用异步红石"
      disable-on-error.comment = "出错时是否禁用异步系统"
      event-class-blacklist.comment = "事件类黑名单"
      mod-blacklist.comment = "模组黑名单"
      strict-class-checking.comment = "是否启用严格类检查"
      timeout-seconds.comment = "超时时间（秒）"
    }
    world-creation {
      comment = "世界创建相关设置"
      fast-world-creation.comment = "是否启用快速世界创建"
      skip-spawn-chunk-loading.comment = "是否跳过出生点区块加载"
      force-close-loading-screen.comment = "是否强制关闭加载屏幕"
      early-world-list-addition.comment = "是否提前添加到世界列表"
      parallel-world-initialization.comment = "是否并行初始化世界"
      world-init-timeout-seconds.comment = "世界初始化超时时间（秒）"
      max-concurrent-world-loads.comment = "最大并发世界加载数"
      optimize-world-border-setup.comment = "是否优化世界边界设置"
      defer-spawn-area-preparation.comment = "是否延迟出生区域准备"
      spawn-area-radius.comment = "出生区域半径"
      async-world-data-loading.comment = "是否异步加载世界数据"
    }
  }
  async-catcher.comment = [
    "异步捕获相关设置"
    "Async Catcher 共有四种模式"
    "NONE - 保持在当前线程执行"
    "DISPATCH - 不阻塞地发布到主线程"
    "BLOCK - 发布到主线程并等待结果"
    "EXCEPTION - 抛出异常"
  ]
  async-catcher.dump.comment = "是否在 debug 日志中打印堆栈信息"
  async-world-save.comment = [
    "异步世界保存相关设置"
    "在服务器关闭时异步保存世界数据，减少关服等待时间"
  ]
  async-world-save.enabled.comment = "是否启用异步世界保存功能"
  async-world-save.timeout-seconds.comment = [
    "异步保存超时时间（秒）"
    "如果在指定时间内保存未完成，服务器将继续关闭流程"
  ]
  async-world-save.save-world-data.comment = "是否在异步保存中包含世界数据"
  compatibility {
    symlink-world.comment = [
      "为模组的维度创建符号链接"
      "推荐启用以增强插件交互兼容性"
      "变更此设置会导致模组世界名称变化，可能造成依赖世界名称的插件数据丢失"
      "参见 https://github.com/IzzelAliz/Arclight/wiki/World-Symlink"
    ]
    extra-logic-worlds.comment = [
      "额外运行逻辑的维度类名"
      "如果有模组世界/功能运行不正常，尝试在日志中搜索和 [EXT_LOGIC] 有关的对应类名并添加"
    ]
    forward-permission.comment = [
      "true - 将 Forge 权限查询请求转发至 Bukkit"
      "false - 不启用权限转发"
      "reverse - 将 Bukkit 玩家权限查询请求转发至 Forge"
    ]
    valid-username-regex.comment = [
      "用户名合法检查正则表达式，留空为使用原版检查"
      "如果需要允许中文字符可以使用"
      "valid-username-regex = \"^[ -~\\p{sc=Han}]{1,16}$\""
      "如果允许任何用户名可以使用"
      "valid-username-regex = \".+\""
    ]
    lenient-item-tag-match.comment = [
      "允许空 NBT 标签的物品和没有 NBT 标签的物品堆叠"
    ]
  }
  velocity {
    comment = "Velocity Modern 转发相关设置"
    enabled.comment = [
      "是否启用 Velocity Modern 转发支持"
      "启用后可以与 Velocity 代理服务器配合使用"
    ]
    online-mode.comment = [
      "是否启用在线模式验证"
      "通常应与 Velocity 配置中的 online-mode 设置保持一致"
    ]
    forwarding-secret.comment = [
      "Velocity 转发密钥"
      "必须与 Velocity 配置文件中的 forwarding-secret 完全一致"
      "用于验证来自 Velocity 的连接请求"
    ]
    debug-logging.comment = [
      "是否在日志中显示 Velocity 转发相关的调试信息"
      "启用后可以帮助诊断玩家连接问题"
    ]
  }
}

# Missing but actually used keys
implementer {
  error = "实现器发生错误: {}"
}


