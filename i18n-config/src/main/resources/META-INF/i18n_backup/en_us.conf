logo = [
  ""
  ""
  "    §1    __                    _                       "
  "    §9   / /   __  ______ ___  (_)___  ____ __________ _"
  "    §3  / /   / / / / __ `__ \\/ / __ \\/ __ `/ ___/ __ `/"
  "    §6 / /___/ /_/ / / / / / / / / / / /_/ / /  / /_/ / "
  "    §e/_____/\\__,_/_/ /_/ /_/_/_/ /_/\\__,_/_/   \\__,_/  "
  ""
  "    §eLuminara Server-Software By§b QianMoo0121(QianMo_ProMax)"
  "    §aVersion {} / {}"
  "    §aBuild Time: {}"
  ""
]
# Translate the word in parenthesis only.
# If there's same Chinese word with same meaning in your language(i.e. Kanji in Japanese), then remove the parenthesis.
release-name {
  Horn = "角 (Horn)"
  GreatHorn = "大角 (Great Horn)"
  Executions = "折威 (Executions)"
  Trials = "顿顽 1.20.1 (Trials)"
}
java {
  deprecated = [
    "You are running an outdated Java version"
    "Current {0} Recommended {1}"
    "Current Java will not be supported in future"
  ]
}

implementer {
  not-found = "Class not found {}"
  error = "Error occured {}"
}
i18n {
  current-not-available = "Current locale {0} is not available"
  using-language = "Using locale {0} and fallback locale {1}"
}
loading-mapping = "Loading mappings ..."
mixin-load {
  core = "Luminara core mixin added."
  optimization = "Luminara optimization mixin added."
}
mod-load = "Luminara Mod loaded."
patcher {
  loading = "Loading plugin patchers ..."
  loaded = "Loaded {} patchers"
  load-error = "Error occurred while loading patcher"
}
registry {
  forge-event = "Luminara events registered."
  begin = "Registering for Bukkit ..."
  error = "Error occured registering Forge "
  enchantment = "Registered {} enchantments"
  potion = "Registered {} new potion effect types"
  material = "Registered {} new materials with {} blocks and {} items"
  entity-type = "Registered {} new entity types"
  environment = "Registered {} new dimensions"
  villager-profession = "Registered {} new villager professions"
  biome = "Registered {} new biomes"
  initialization-error = "Error initializing Arclight"
  meta-type {
    not-subclass = "{} is not a subclass of {}"
    error = "{} provided itemMetaType {} is invalid: {}"
    no-candidate = "{} did not find a valid constructor in prodived itemMetaType {}"
  }
  block-state {
    not-subclass = "{} is not a subclass of {}"
    error = "{} prodived itemMetaType {} is invalid: {}"
    no-candidate = "{} did not find a valid constructor in provided blockStateClass {}"
  }
  entity {
    not-subclass = "{} is not a subclass of {}"
    error = "{} prodived entityClass {} is invalid: {}"
  }
  debug {
    registered-cooking-category = "Registered {} as cooking category {}"
    registered-spawn-category = "Registered {} as spawn category {}"
    registered-custom-stats = "Registered {} as custom stats {}"
    registered-biome = "Registered {} as biome {}"
    registered-environment = "Registered {} as environment {}"
    registered-entity = "Registered {} as entity {}"
    registered-enchantment = "Registered {} as enchantment {}"
    failed-register-enchantment = "Failed to register enchantment {}: {}"
    registered-potion = "Registered {} as potion {}"
    failed-register-potion = "Failed to register potion type {}: {}"
    registered-block = "Registered {} as block {}"
    registered-item = "Registered {} as item {}"
    not-found-entity = "Not found {} in {}"
  }
  event-handler {
    registration-error = "Error registering event handler: {} {}"
    invalid-signature = "{} attempted to register an invalid EventHandler method signature \"{}\" in {}"
    plugin-failed-register = "Plugin {} has failed to register events for {} because {} does not exist."
  }
  entity-mapping {
    no-valid-mapping = "{} has no valid entity class mapping"
    missing-mapping-error = "Missing valid entity class mapping"
  }
  plugin {
    load-error-invalid-name = "Error loading plugin '{}' (directory {}): invalid name"
    load-error-space-in-name = "Error loading plugin '{}' (directory {}): plugin name contains spaces"
    load-error-general = "Error loading plugin '{}' (directory {}): {}"
    load-error-simple = "Error loading plugin '{}' (directory {})"
    enabling = "Enabling plugin {}"
    disabling = "Disabling plugin {}"
  }
}

# Server initialization messages
server-init {
  failed-initialize-server = "Failed to initialize server"
}

# Class loading and cache messages
class-cache {
  obsolete-cleared = "Obsolete plugin class cache is cleared"
  failed-initialize = "Failed to initialize class cache"
  failed-close = "Failed to close class cache"
  cannot-find-package = "Cannot find package {}"
}

# Component bridge messages
component-bridge {
  initialized-successfully = "ComponentBridgeHandler initialized successfully with method: {}"
  could-not-find-method = "Could not find getSiblings method in Component class"
  failed-initialize = "Failed to initialize ComponentBridgeHandler: {}"
}


# Server lifecycle messages
server {
  starting = "Starting Luminara server..."
  started = "Luminara server startup completed! Took {} milliseconds"
  stopping = "Stopping Luminara server..."
  stopped = "Luminara server has stopped"
  crash-report-saved = "Luminara has saved the server crash report to {}, please check!"
  crash-report-failed = "Unable to save server crash report to disk, please check disk space and permissions!"
  unexpected-exception = "Encountered unexpected exception"
  overload-warning = "Can't keep up! Is the server overloaded? Running {} milliseconds behind!"
  exception-stopping = "Exception stopping the server"
  async-world-save-starting = "Starting async world save during server shutdown..."
  async-world-save-starting-general = "Starting async world save..."
  async-world-save-failed = "Failed to save world {}"
  saving-chunks = "Saving chunks for world '{}'"
  minecraft-version = "Starting Minecraft server for client version {}"
  java-memory-too-low = "Insufficient memory allocated for server startup, please allocate at least 1024MB! Reference: \"java -Xmx1024M -Xms1024M -jar server.jar\""
  loading-properties = "Loading server configuration file (server.properties)..."
  default-gamemode = "Default game mode: {}"
  starting-server = "Starting Minecraft server on {}:{}"
  player-auth-warning1 = "/// !!! Luminara Warning !!! ///"
  player-auth-warning2 = "Your server is in offline mode and will not authenticate player names, please ensure server security!"
  player-auth-warning3 = "Offline mode allows LAN players to join without authentication, but also poses security risks!"
  player-auth-warning4 = "To enable online mode, set \"online-mode\" to \"true\" in server.properties!"
  bind-port-warning1 = "/// !!! Luminara Warning !!! ///"
  bind-port-warning2 = "Unexpected situation occurred: {}"
  bind-port-warning3 = "Port {} may already be in use by another server!"
  start-done = "Luminara startup completed! Total time: {}, use /help for command help!"
}

# Optimization system messages
optimization {
  chunk {
    unloading = "Unloading chunk [{}, {}] in world {}"
    unloaded = "Unloaded {} chunks"
    rate-limit = "Chunk loading rate limit: original {} -> limited {}"
  }
  memory {
    cleanup-start = "Starting memory cleanup..."
    cleanup-complete = "Memory cleanup completed, freed {} MB"
    high-usage = "High memory usage: {}%"
    gc-triggered = "Garbage collection triggered"
    cache-cleanup-completed = "Luminara cache cleanup completed"
    cache-cleanup-error = "Error during cache cleanup"
    cache-cleanup-failed = "Cache cleanup failed"
  }
  manager {
    shutdown-error = "Error during optimization system shutdown"
  }
  async-ai {
    calculation-error = "Error during async AI calculation"
    processing-error = "Error processing entity {} in async AI"
    ai-calculation-error = "Error in AI calculations"
  }
  async-collision {
    calculation-error = "Error during async collision calculation"
    processing-error = "Error processing entity {} in async collision"
    check-error = "Error during collision check"
    handling-error = "Error handling async collision between {} and {}"
    calculation-general-error = "Error in collision calculations"
  }
  async-event {
    disabled-due-to-errors = "Disabled async processing for event type due to errors: {}"
    handler-error = "Error in async event handler for event {}"
    registered = "Registered async event: {}"
    interrupted-shutdown = "Interrupted during shutdown"
    registered-sync = "Registered sync event: {}"
  }
}

# Velocity forwarding messages
velocity {
  enabled = "Velocity Modern forwarding enabled"
  disabled = "Velocity Modern forwarding disabled"
  loaded-argument-types = "Loaded {} integration argument types"
  failed-load-argument-types = "Failed to load integration argument types, using defaults"
}

# Error messages
error {
  class-not-found = "Class not found: {}"
  method-not-found = "Method not found: {}"
  field-not-found = "Field not found: {}"
  invalid-configuration = "Invalid configuration file: {}"
  file-not-found = "File not found: {}"
  permission-denied = "Permission denied: {}"
  network-error = "Network error: {}"
  database-error = "Database error: {}"
  plugin-error = "Plugin error: {}"
  mixin-error = "Mixin error: {}"
}

# Warning messages
warning {
  deprecated-api = "Using deprecated API: {}"
  performance-issue = "Performance issue detected: {}"
  memory-low = "Low memory warning, current usage: {}%"
  disk-space-low = "Low disk space: {} MB remaining"
  plugin-conflict = "Plugin conflict detected: {} may conflict with {}"
  async-operation = "Async operation warning: {}"
}

# World management messages
world {
  creating = "Creating world {}"
  created = "World {} created"
  loading = "Loading world {}"
  loaded = "World {} loaded"
  unloading = "Unloading world {}"
  unloaded = "World {} unloaded"
  saving = "Saving world: {}"
  saved-successfully = "World {} saved successfully"
  save-error = "Error saving world {}"
}

# Mod integration messages
mod {
  conflict-detected = "Mod conflict detected: {} vs {}"
  conflict-fatal = "Fatal mod conflict! These mods are incompatible and the server will stop. Please remove one of the mods and restart the server."
}

# Sign block entity messages
sign {
  non-editable-warning = "Player {} just tried to change non-editable sign"
}

# Enum extender messages
enum {
  not-found-warning = "Expected to find {} in {}, but not found"
}

# World symlink messages
symlink {
  create-error = "Error creating symlink"
  file-exist = "File already exists when creating symbol link {}"
  error-symlink = "File system do not support symbol links"
}

# Distribution validation messages
dist {
  logic-world-check = "Level class {} considered as logic world: {}"
}

# Chat system messages
chat {
  message-too-long = "Chat message too long!"
  empty-message-warning = "{} tried to send empty message"
  long-message-warning = "{} tried to send too long message: {} characters"
  illegal-characters = "Chat message contains illegal characters"
  player-removed = "Player has been removed, cannot send message"
}

# Player action messages
player {
  dropped-items-quickly = "{} dropped items too quickly!"
  dropped-items-disconnect = "You dropped items too quickly (cheating?)"
  invalid-hotbar = "{} tried to set invalid hotbar selection"
  invalid-hotbar-disconnect = "Invalid hotbar selection (cheating?)"
  command-issued = "{} issued server command: {}"
  internal-command-error = "Internal error occurred while executing this command"
  book-edited-quickly = "Book edited too quickly!"
}

comments {
  _v.comment = [
    "Repository: https://github.com/QianMoo0121/Luminara"
    "Issue Tracker: https://github.com/QianMoo0121/Luminara/issues"
    ""
    ""
    "Config version number, do not edit."
  ]
  locale.comment = "Language/I18n settings"
  optimization {
    comment = "Server optimization related settings"
    cache-plugin-class.comment = "Whether to cache plugin classes for better performance"
    goal-selector-update-interval.comment = [
      "Entity goal selector update interval"
      "Higher values consume less resources"
      "Causes entities to change goals less frequently"
    ]
    entity-optimization {
      comment = "Entity optimization related settings"
      disable-entity-collisions.comment = "Whether to disable entity collision detection"
      entity-cleanup-enabled.comment = "Whether to enable entity cleanup feature"
      entity-cleanup-threshold.comment = "Entity count threshold to trigger cleanup"
      entity-freeze-timeout.comment = "Entity freeze timeout (milliseconds)"
      reduce-entity-updates.comment = "Whether to reduce entity update frequency"
      clean-valuable-items.comment = "Whether to clean valuable items"
      item-max-age.comment = "Maximum item existence time (ticks)"
      cleanup-notification-enabled.comment = "Whether to enable cleanup notifications"
      cleanup-warning-time.comment = "Cleanup warning time (seconds)"
      cleanup-start-message.comment = "Cleanup start message template"
      cleanup-complete-message.comment = "Cleanup complete message template"
      cleanup-cancelled-message.comment = "Cleanup cancelled message template"
      entity-check-interval.comment = "Entity check interval (ticks)"
      entity-update-distance.comment = "Entity update distance"
      max-entities-per-chunk.comment = "Maximum entities per chunk"
      max-entities-per-type.comment = "Maximum entities per type"
      chunk-entity-limit.comment = "Chunk entity limit"
    }
    chunk-optimization {
      comment = "Chunk optimization related settings"
      aggressive-chunk-unloading.comment = "Whether to enable aggressive chunk unloading"
      chunk-unload-delay.comment = "Chunk unload delay (ticks)"
      optimize-chunk-loading.comment = "Whether to optimize chunk loading"
      chunk-load-rate-limit.comment = "Chunk loading rate limit"
    }
    memory-optimization {
      comment = "Memory optimization related settings"
      entity-cleanup-enabled.comment = "Whether to enable entity cleanup"
      cache-cleanup-enabled.comment = "Whether to enable cache cleanup"
      cache-cleanup-interval.comment = "Cache cleanup interval (seconds)"
    }
    async-system {
      comment = "Async system related settings"
      enabled.comment = "Whether to enable async system"
      max-threads.comment = "Maximum thread count"
      async-ai-enabled.comment = "Whether to enable async AI"
      async-collision-enabled.comment = "Whether to enable async collision detection"
      async-redstone-enabled.comment = "Whether to enable async redstone"
      disable-on-error.comment = "Whether to disable async system on error"
      event-class-blacklist.comment = "Event class blacklist"
      mod-blacklist.comment = "Mod blacklist"
      strict-class-checking.comment = "Whether to enable strict class checking"
      timeout-seconds.comment = "Timeout (seconds)"
    }
    world-creation {
      comment = "World creation related settings"
      fast-world-creation.comment = "Whether to enable fast world creation"
      skip-spawn-chunk-loading.comment = "Whether to skip spawn chunk loading"
      force-close-loading-screen.comment = "Whether to force close loading screen"
      early-world-list-addition.comment = "Whether to add to world list early"
      parallel-world-initialization.comment = "Whether to initialize worlds in parallel"
      world-init-timeout-seconds.comment = "World initialization timeout (seconds)"
      max-concurrent-world-loads.comment = "Maximum concurrent world loads"
      optimize-world-border-setup.comment = "Whether to optimize world border setup"
      defer-spawn-area-preparation.comment = "Whether to defer spawn area preparation"
      spawn-area-radius.comment = "Spawn area radius"
      async-world-data-loading.comment = "Whether to load world data asynchronously"
    }
  }
  async-catcher.comment = [
    "Async Catcher related settings"
    "There are four modes, and BLOCK is recommended"
    "NONE - Do nothing"
    "DISPATCH - Non blocking-ly dispatch action to main thread"
    "BLOCK - Run in main thread and wait for results"
    "EXCEPTION - Raise an error"
  ]
  async-catcher.dump.comment = "Dump stack trace information in debug.log"
  async-world-save.comment = [
    "Async world save related settings"
    "Asynchronously save world data during server shutdown to reduce shutdown time"
  ]
  async-world-save.enabled.comment = "Whether to enable async world save feature"
  async-world-save.timeout-seconds.comment = [
    "Async save timeout in seconds"
    "If save doesn't complete within this time, server will continue shutdown process"
  ]
  async-world-save.save-world-data.comment = "Whether to include world data in async save"
  compatibility {
    symlink-world.comment = [
      "Create symbol links to mod dimension folder that matches Bukkit world name"
      "Enable this could improve plugin compotibility"
      "Changing this on production server will cause changes to mod world names"
      "  and cause data loss on plugins relying world names"
      "See https://github.com/IzzelAliz/Arclight/wiki/World-Symlink for more detail"
    ]
    extra-logic-worlds.comment = [
      "Extra worlds running logic"
      "If any mods do not function well, try search class names in logs related to [EXT_LOGIC] and add them here"
    ]
    forward-permission.comment = [
      "true - Forward Forge permission query to Bukkit"
      "false - Disable permission forward"
      "reverse - Forward Bukkit player perimission query to Forge"
    ]
    valid-username-regex.comment = [
      "Regex for valid username check. Leave it blank will fallback to vanilla check"
      "Following allows Chinese characters:"
      "valid-username-regex = \"^[ -~\\\\p{sc=Han}]{1,16}$\""
      "Following allows any username to login:"
      "valid-username-regex = \".+\""
    ]
    lenient-item-tag-match.comment = [
      "Allows items with an empty nbt tag stack on no tag items"
    ]
  }
  velocity {
    comment = "Velocity Modern Forwarding related settings"
    enabled.comment = [
      "Whether to enable Velocity Modern Forwarding support"
      "When enabled, allows integration with Velocity proxy server"
    ]
    online-mode.comment = [
      "Whether to enable online mode verification"
      "Should typically match the online-mode setting in Velocity configuration"
    ]
    forwarding-secret.comment = [
      "Velocity forwarding secret key"
      "Must exactly match the forwarding-secret in Velocity configuration file"
      "Used to authenticate connection requests from Velocity"
    ]
    debug-logging.comment = [
      "Whether to display Velocity forwarding related debug information in logs"
      "Enable this to help diagnose connection issues"
    ]
  }
}
