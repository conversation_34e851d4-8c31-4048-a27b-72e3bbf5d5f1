logo = [
  ""
  ""
  "    §1    __                    _                       "
  "    §9   / /   __  ______ ___  (_)___  ____ __________ _"
  "    §3  / /   / / / / __ `__ \\/ / __ \\/ __ `/ ___/ __ `/"
  "    §6 / /___/ /_/ / / / / / / / / / / /_/ / /  / /_/ / "
  "    §e/_____/\\__,_/_/ /_/ /_/_/_/ /_/\\__,_/_/   \\__,_/  "
  ""
  "    §eLuminara·流明纳拉 Servidor Por§b QianMoo0121(QianMo_ProMax)"
  "    §aVersión {} / {}"
  "    §aFecha de compilación {}"
  ""
]
release-name {
  Horn = "角 (Cuerno)"
  GreatHorn = "大角 (Gran Cuerno)"
  Executions = "折威 (Ejecuciones)"
  Trials = "顿顽 1.20.1 (Pruebas)"
}
java {
  deprecated = [
    "Estás utilizando una versión desactualizada de Java"
    "Actual {0} Recomendada {1}"
    "La versión actual de Java no será soportada en un futuro"
  ]
}

implementer {
  not-found = "Clase no encontrada {}"
  error = "Ha ocurrido un error {}"
}
i18n {
  current-not-available = "El idioma en uso {0} no está disponible"
  using-language = "Usando el idioma {0} y el idioma alternativo {1}"
}
loading-mapping = "Cargando mappings ..."
mixin-load {
  core = "Núcleo de mixin de Luminara añadido."
  optimization = "Optimización de mixin de Luminara añadida."
}
mod-load = "Luminara Mod cargado."
patcher {
  loading = "Cargando plugin patchers ..."
  loaded = "{} patchers cargados"
  load-error = "Ha ocurrido un error cargando el patcher"
}
registry {
  forge-event = "Eventos de Arclight registrados."
  begin = "Registrando Bukkit ..."
  error = "Ha ocurrido un error registrando Forge "
  enchantment = "Registrados {} encantamientos"
  potion = "Registrados {} tipos de efectos de poción nuevos"
  material = "Registrados {} materiales nuevos con {} bloques y {} objetos"
  entity-type = "Registrados {} tipos de entidades nuevos"
  environment = "Registradas {} dimensiones nuevas"
  villager-profession = "Registradas {} profesiones de aldeanos nuevas"
  biome = "Registrados {} biomas nuevos"
  initialization-error = "Error inicializando Arclight"
  meta-type {
    not-subclass = "{} no es una subclase de {}"
    error = "{} itemMetaType proporcionado {} no es válido: {}"
    no-candidate = "{} no encontró un constructor válido en el itemMetaType proporcionado {}"
  }
  block-state {
    not-subclass = "{} no es una subclase de {}"
    error = "{} itemMetaType proporcionado {} no es válido: {}"
    no-candidate = "{} no encontró un constructor válido en el blockStateClass proporcionado {}"
  }
  entity {
    not-subclass = "{} no es una subclase de {}"
    error = "{} entityClass proporcionado {} noe s válido: {}"
  }
  debug {
    registered-cooking-category = "Registrado {} como categoría de cocina {}"
    registered-spawn-category = "Registrado {} como categoría de aparición {}"
    registered-custom-stats = "Registrado {} como estadística personalizada {}"
    registered-biome = "Registrado {} como bioma {}"
    registered-environment = "Registrado {} como entorno {}"
    registered-entity = "Registrado {} como entidad {}"
    registered-enchantment = "Registrado {} como encantamiento {}"
    failed-register-enchantment = "Error al registrar encantamiento {}: {}"
    registered-potion = "Registrado {} como poción {}"
    failed-register-potion = "Error al registrar tipo de poción {}: {}"
    registered-block = "Registrado {} como bloque {}"
    registered-item = "Registrado {} como objeto {}"
    not-found-entity = "No encontrado {} en {}"
  }
  event-handler {
    registration-error = "Error registrando manejador de eventos: {} {}"
    invalid-signature = "{} intentó registrar una firma de método EventHandler inválida \"{}\" en {}"
    plugin-failed-register = "El plugin {} falló al registrar eventos para {} porque {} no existe."
  }
  entity-mapping {
    no-valid-mapping = "{} no tiene mapeo de clase de entidad válido"
    missing-mapping-error = "Falta mapeo de clase de entidad válido"
  }
  plugin {
    load-error-invalid-name = "Error cargando plugin '{}' (directorio {}): nombre inválido"
    load-error-space-in-name = "Error cargando plugin '{}' (directorio {}): el nombre del plugin contiene espacios"
    load-error-general = "Error cargando plugin '{}' (directorio {}): {}"
    load-error-simple = "Error cargando plugin '{}' (directorio {})"
    enabling = "Habilitando plugin {}"
    disabling = "Deshabilitando plugin {}"
  }
}

# Server initialization messages
server-init {
  failed-initialize-server = "Error al inicializar el servidor"
}

# Class loading and cache messages
class-cache {
  obsolete-cleared = "Caché de clases de plugin obsoleto limpiado"
  failed-initialize = "Error al inicializar caché de clases"
  failed-close = "Error al cerrar caché de clases"
  cannot-find-package = "No se puede encontrar el paquete {}"
}

# Component bridge messages
component-bridge {
  initialized-successfully = "ComponentBridgeHandler inicializado exitosamente con método: {}"
  could-not-find-method = "No se pudo encontrar el método getSiblings en la clase Component"
  failed-initialize = "Error al inicializar ComponentBridgeHandler: {}"
}
error-symlink = "El sistema de ficheros no soporta enlaces de símbolos"
symlink-file-exist = "El archivo ya existe al crear el enlace simbólico {}"

# Server lifecycle messages
server {
  starting = "Iniciando servidor Luminara..."
  started = "¡Inicio del servidor Luminara completado! Tomó {} milisegundos"
  stopping = "Deteniendo servidor Luminara..."
  stopped = "El servidor Luminara se ha detenido"
  crash-report-saved = "¡Luminara ha guardado el reporte de crash del servidor en {}, por favor revísalo!"
  crash-report-failed = "¡No se pudo guardar el reporte de crash del servidor al disco, revisa el espacio y permisos!"
  unexpected-exception = "Se encontró una excepción inesperada"
  overload-warning = "¡No puede seguir el ritmo! ¿Está sobrecargado el servidor? ¡{} milisegundos de retraso!"
  exception-stopping = "Excepción al detener el servidor"
  async-world-save-starting = "Iniciando guardado asíncrono de mundo durante el cierre del servidor..."
  async-world-save-starting-general = "Iniciando guardado asíncrono de mundo..."
  async-world-save-failed = "Error al guardar mundo {}"
  saving-chunks = "Guardando chunks del mundo '{}'"
  minecraft-version = "Iniciando servidor de Minecraft para la versión de cliente {}"
  java-memory-too-low = "Memoria insuficiente asignada para el inicio del servidor, ¡asigna al menos 1024MB! Referencia: \"java -Xmx1024M -Xms1024M -jar server.jar\""
  loading-properties = "Cargando archivo de configuración del servidor (server.properties)..."
  default-gamemode = "Modo de juego predeterminado: {}"
  starting-server = "Iniciando servidor de Minecraft en {}:{}"
  player-auth-warning1 = "/// !!! Advertencia de Luminara !!! ///"
  player-auth-warning2 = "¡Tu servidor está en modo offline y no autenticará nombres de jugadores, asegura la seguridad del servidor!"
  player-auth-warning3 = "¡El modo offline permite que jugadores LAN se unan sin autenticación, pero también presenta riesgos de seguridad!"
  player-auth-warning4 = "¡Para habilitar el modo online, establece \"online-mode\" a \"true\" en server.properties!"
  bind-port-warning1 = "/// !!! Advertencia de Luminara !!! ///"
  bind-port-warning2 = "Situación inesperada ocurrida: {}"
  bind-port-warning3 = "¡El puerto {} puede estar ya en uso por otro servidor!"
  start-done = "¡Inicio de Luminara completado! Tiempo total: {}, usa /help para ayuda de comandos!"
}

# Optimization system messages
optimization {
  chunk {
    unloading = "Descargando chunk [{}, {}] en el mundo {}"
    unloaded = "{} chunks descargados"
    rate-limit = "Límite de velocidad de carga de chunks: original {} -> limitado {}"
  }
  memory {
    cleanup-start = "Iniciando limpieza de memoria..."
    cleanup-complete = "Limpieza de memoria completada, liberados {} MB"
    high-usage = "Uso alto de memoria: {}%"
    gc-triggered = "Recolección de basura activada"
    cache-cleanup-completed = "Limpieza de caché de Luminara completada"
    cache-cleanup-error = "Error durante la limpieza de caché"
    cache-cleanup-failed = "Falló la limpieza de caché"
  }
  manager {
    shutdown-error = "Error durante el cierre del sistema de optimización"
  }
  async-ai {
    calculation-error = "Error durante el cálculo de IA asíncrona"
    processing-error = "Error procesando entidad {} en IA asíncrona"
    ai-calculation-error = "Error en cálculos de IA"
  }
  async-collision {
    calculation-error = "Error durante el cálculo de colisión asíncrona"
    processing-error = "Error procesando entidad {} en colisión asíncrona"
    check-error = "Error durante la verificación de colisión"
    handling-error = "Error manejando colisión asíncrona entre {} y {}"
    calculation-general-error = "Error en cálculos de colisión"
  }
  async-event {
    disabled-due-to-errors = "Procesamiento asíncrono deshabilitado para tipo de evento debido a errores: {}"
    handler-error = "Error en el manejador de eventos asíncrono para el evento {}"
    registered = "Evento asíncrono registrado: {}"
    interrupted-shutdown = "Interrumpido durante el cierre"
    registered-sync = "Evento síncrono registrado: {}"
  }
}

# Velocity forwarding messages
velocity {
  enabled = "Reenvío Velocity Modern habilitado"
  disabled = "Reenvío Velocity Modern deshabilitado"
  loaded-argument-types = "{} tipos de argumentos de integración cargados"
  failed-load-argument-types = "Falló la carga de tipos de argumentos de integración, usando valores por defecto"
}

# Error messages
error {
  class-not-found = "Clase no encontrada: {}"
  method-not-found = "Método no encontrado: {}"
  field-not-found = "Campo no encontrado: {}"
  invalid-configuration = "Archivo de configuración inválido: {}"
  file-not-found = "Archivo no encontrado: {}"
  permission-denied = "Permiso denegado: {}"
  network-error = "Error de red: {}"
  database-error = "Error de base de datos: {}"
  plugin-error = "Error de plugin: {}"
  mixin-error = "Error de Mixin: {}"
}

# Warning messages
warning {
  deprecated-api = "Usando API obsoleta: {}"
  performance-issue = "Problema de rendimiento detectado: {}"
  memory-low = "Advertencia de memoria baja, uso actual: {}%"
  disk-space-low = "Espacio en disco bajo: {} MB restantes"
  plugin-conflict = "Conflicto de plugin detectado: {} puede entrar en conflicto con {}"
  async-operation = "Advertencia de operación asíncrona: {}"
}

# World management messages
world {
  creating = "Creando mundo {}"
  created = "Mundo {} creado"
  loading = "Cargando mundo {}"
  loaded = "Mundo {} cargado"
  unloading = "Descargando mundo {}"
  unloaded = "Mundo {} descargado"
  saving = "Guardando mundo: {}"
  saved-successfully = "Mundo {} guardado exitosamente"
  save-error = "Error guardando mundo {}"
}

# Mod integration messages
mod {
  conflict-detected = "Conflicto de mod detectado: {} vs {}"
}

# Sign block entity messages
sign {
  non-editable-warning = "El jugador {} intentó cambiar un cartel no editable"
}

# Enum extender messages
enum {
  not-found-warning = "Se esperaba encontrar {} en {}, pero no se encontró"
}

# World symlink messages
symlink {
  create-error = "Error creando enlace simbólico"
}

# Distribution validation messages
dist {
  logic-world-check = "Clase de nivel {} considerada como mundo lógico: {}"
}

# Chat system messages
chat {
  message-too-long = "¡Mensaje de chat demasiado largo!"
  empty-message-warning = "{} intentó enviar un mensaje vacío"
  long-message-warning = "{} intentó enviar un mensaje demasiado largo: {} caracteres"
  illegal-characters = "El mensaje de chat contiene caracteres ilegales"
  player-removed = "El jugador ha sido removido, no se puede enviar el mensaje"
}

# Player action messages
player {
  dropped-items-quickly = "¡{} soltó objetos demasiado rápido!"
  dropped-items-disconnect = "Soltaste objetos demasiado rápido (¿haciendo trampa?)"
  invalid-hotbar = "{} intentó establecer una selección de barra de acceso rápido inválida"
  invalid-hotbar-disconnect = "Selección de barra de acceso rápido inválida (¿haciendo trampa?)"
  command-issued = "{} ejecutó comando del servidor: {}"
  internal-command-error = "Ocurrió un error interno al ejecutar este comando"
  book-edited-quickly = "¡Libro editado demasiado rápido!"
}

comments {
  _v.comment = [
    "Repositorio: https://github.com/QianMoo0121/Luminara"
    "Rastredor de asuntos y errores: https://github.com/QianMoo0121/Luminara/issues"
    ""
    ""
    "Versión de la configuración, no editar."
  ]
  locale.comment = "Idioma/ajustes I18n"
  optimization {
    comment = "Ajustes relacionados con la optimización"
    goal-selector-update-interval.comment = [
      "Intervalo en ticks para actualizar el selector de objetivos (goal selector)"
      "Valores más grandes gastan menos recursos"
      "Hace que los mobs cambien sus objetivos menos a menudo"
    ]
  }
  async-catcher.comment = [
    "Ajustes relacionados con receptor asíncromo (Async Catcher)"
    "Hay cuatro modos, y BLOCK (bloqueo) es el recomendado"
    "NONE - No hacer nada"
    "DISPATCH - Enviar acciones sin interrumpir el hilo principal (main thread)"
    "BLOCK - Ejecutar en el hilo principal (main thread) y esperar los resultados"
    "EXCEPTION - Generar un error"
  ]
  async-catcher.dump.comment = "Generar un volcado de información (stack trace) en debug.log"
  async-world-save.comment = [
    "Configuración relacionada con el guardado asíncrono del mundo"
    "Guardar datos del mundo de forma asíncrona durante el cierre del servidor para reducir el tiempo de cierre"
  ]
  async-world-save.enabled.comment = "Si habilitar la función de guardado asíncrono del mundo"
  async-world-save.timeout-seconds.comment = [
    "Tiempo de espera del guardado asíncrono en segundos"
    "Si el guardado no se completa en este tiempo, el servidor continuará el proceso de cierre"
  ]
  async-world-save.save-world-data.comment = "Si incluir datos del mundo en el guardado asíncrono"
  compatibility {
    symlink-world.comment = [
      "Crear enlaces de símbolos a las dimensiones de mods para que se ajuste al formato de Bukkit"
      "Activando esto puede mejorar la compatibilidad entre plugins"
      "Cambiar esto en servidores en producción causará cambios a nombres de mundos modificados (mod world names)"
      "  y causará pérdida de información en plugins que dependan en los nombres de mundos"
      "Lee https://github.com/IzzelAliz/Arclight/wiki/World-Symlink para más detalles"
    ]
    extra-logic-worlds.comment = [
      "Lógica de los mundos adicionales en ejecución"
      "Si algún mod no funciona correctamente, intenta buscar en los logs los nombres de las clases relacionados con [EXT_LOGIC] y añádelos aquí"
    ]
    forward-permission.comment = [
      "true - Reenviar consultas de permisos de Forge a Bukkit"
      "false - Desactivar el reenvío de permisos"
      "reverse - Reenviar consultas de permisos de jugadores de Bukkit a Forge"
    ]
    valid-username-regex.comment = [
      "Expresión regular para verificación de nombres de usuario válidos. Déjalo en blanco para usar la verificación vanilla"
      "Lo siguiente permite caracteres chinos:"
      "valid-username-regex = \"^[ -~\\\\p{sc=Han}]{1,16}$\""
      "Lo siguiente permite cualquier nombre de usuario para iniciar sesión:"
      "valid-username-regex = \".+\""
    ]
    lenient-item-tag-match.comment = [
      "Permite que los elementos con una etiqueta nbt vacía se apilen en elementos sin etiqueta"
    ]
  }
  velocity {
    comment = "Configuración relacionada con Velocity Modern Forwarding"
    enabled.comment = [
      "Si habilitar el soporte de Velocity Modern Forwarding"
      "Cuando está habilitado, permite la integración con el servidor proxy Velocity"
    ]
    online-mode.comment = [
      "Si habilitar la verificación del modo en línea"
      "Normalmente debería coincidir con la configuración online-mode en la configuración de Velocity"
    ]
    forwarding-secret.comment = [
      "Clave secreta de reenvío de Velocity"
      "Debe coincidir exactamente con forwarding-secret en el archivo de configuración de Velocity"
      "Se usa para autenticar las solicitudes de conexión de Velocity"
    ]
    debug-logging.comment = [
      "Si mostrar información de depuración relacionada con el reenvío de Velocity en los logs"
      "Habilita esto para ayudar a diagnosticar problemas de conexión"
    ]
  }
}
