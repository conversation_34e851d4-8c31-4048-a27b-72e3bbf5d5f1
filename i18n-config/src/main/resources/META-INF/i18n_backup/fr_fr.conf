logo = [
  ""
  ""
  "    §1    __                    _                       "
  "    §9   / /   __  ______ ___  (_)___  ____ __________ _"
  "    §3  / /   / / / / __ `__ \\/ / __ \\/ __ `/ ___/ __ `/"
  "    §6 / /___/ /_/ / / / / / / / / / / /_/ / /  / /_/ / "
  "    §e/_____/\\__,_/_/ /_/ /_/_/_/ /_/\\__,_/_/   \\__,_/  "
  ""
  "    §eLuminara·流明纳拉 Serveur Par§b QianMoo0121(QianMo_ProMax)"
  "    §aVersion {} / {}"
  "    §aDate du Build {}"
  ""
]
release-name {
  Horn = "角 (Corne)"
  GreatHorn = "大角 (Grande Corne)"
  Executions = "折威 (Exécutions)"
  Trials = "顿顽 1.20.1 (Épreuves)"
}

implementer {
  not-found = "Class non trouvée {}"
  error = "Erreur survenue {}"
}
i18n {
  current-not-available = "Les paramètres de langue {0} ne sont pas disponibles"
  using-language = "Utilisation des paramètres de langue {0} et des paramètres de langue de secours {1}"
}
loading-mapping = "Chargement des mappings ..."
mixin-load {
  core = "Luminara core mixin ajouté."
  optimization = "Luminara optimization mixin ajouté."
}
mod-load = "Luminara Mod chargé."
patcher {
  loading = "Chargement des patchs de plugins ..."
  loaded = "{} patchs chargés"
  load-error = "Une erreur s'est produite lors du chargement d'un patch"
}
registry {
  forge-event = "Événements Arclight enregistrés."
  begin = "Enregistrement de Bukkit ..."
  error = "Une erreur s'est produite lors de l'enregistrement de Forge "
  enchantment = "{} enchantements enregistrés"
  potion = "{} nouveaux effets de potions enregistrés"
  material = "{} nouveaux materiaux enregistrés dont {} blocs et {} objets"
  entity-type = "{} nouveaux types d'entités enregistrés"
  environment = "{} nouvelles dimensions enregistrés"
  villager-profession = "{} nouvelles professions de villageois enregistrés"
  biome = "{} nouveaux biomes enregistrés"
  initialization-error = "Erreur lors de l'initialisation d'Arclight"
  meta-type {
    not-subclass = "{} n'est pas une sous-classe de {}"
    error = "{} itemMetaType {} fourni n'est pas valide: {}"
    no-candidate = "{} aucun constructeur valide trouvé pour itemMetaType {}"
  }
  block-state {
    not-subclass = "{} n'est pas une sous-classe de {}"
    error = "{} itemMetaType {} fourni n'est pas valide: {}"
    no-candidate = "{} aucun constructeur valide trouvé pour blockStateClass {}"
  }
  entity {
    not-subclass = "{} n'est pas une sous-classe de {}"
    error = "{} entityClass {} fourni n'est pas valide: {}"
  }
  debug {
    registered-cooking-category = "Enregistré {} comme catégorie de cuisine {}"
    registered-spawn-category = "Enregistré {} comme catégorie d'apparition {}"
    registered-custom-stats = "Enregistré {} comme statistique personnalisée {}"
    registered-biome = "Enregistré {} comme biome {}"
    registered-environment = "Enregistré {} comme environnement {}"
    registered-entity = "Enregistré {} comme entité {}"
    registered-enchantment = "Enregistré {} comme enchantement {}"
    failed-register-enchantment = "Échec de l'enregistrement de l'enchantement {} : {}"
    registered-potion = "Enregistré {} comme potion {}"
    failed-register-potion = "Échec de l'enregistrement du type de potion {} : {}"
    registered-block = "Enregistré {} comme bloc {}"
    registered-item = "Enregistré {} comme objet {}"
    not-found-entity = "Non trouvé {} dans {}"
  }
  event-handler {
    registration-error = "Erreur lors de l'enregistrement du gestionnaire d'événements : {} {}"
    invalid-signature = "{} a tenté d'enregistrer une signature de méthode EventHandler invalide \"{}\" dans {}"
    plugin-failed-register = "Le plugin {} a échoué à enregistrer les événements pour {} car {} n'existe pas."
  }
  entity-mapping {
    no-valid-mapping = "{} n'a pas de mappage de classe d'entité valide"
    missing-mapping-error = "Mappage de classe d'entité valide manquant"
  }
  plugin {
    load-error-invalid-name = "Erreur lors du chargement du plugin '{}' (répertoire {}) : nom invalide"
    load-error-space-in-name = "Erreur lors du chargement du plugin '{}' (répertoire {}) : le nom du plugin contient des espaces"
    load-error-general = "Erreur lors du chargement du plugin '{}' (répertoire {}) : {}"
    load-error-simple = "Erreur lors du chargement du plugin '{}' (répertoire {})"
    enabling = "Activation du plugin {}"
    disabling = "Désactivation du plugin {}"
  }
}

# Server initialization messages
server-init {
  failed-initialize-server = "Échec de l'initialisation du serveur"
}

# Class loading and cache messages
class-cache {
  obsolete-cleared = "Cache de classes de plugin obsolète nettoyé"
  failed-initialize = "Échec de l'initialisation du cache de classes"
  failed-close = "Échec de la fermeture du cache de classes"
  cannot-find-package = "Impossible de trouver le package {}"
}

# Component bridge messages
component-bridge {
  initialized-successfully = "ComponentBridgeHandler initialisé avec succès avec la méthode : {}"
  could-not-find-method = "Impossible de trouver la méthode getSiblings dans la classe Component"
  failed-initialize = "Échec de l'initialisation de ComponentBridgeHandler : {}"
}
error-symlink = "Le système de fichiers ne prend pas en charge les liens symboliques"
symlink-file-exist = "Le fichier existe déjà lors de la création du lien symbolique {}"

# Server lifecycle messages
server {
  starting = "Démarrage du serveur Luminara..."
  started = "Démarrage du serveur Luminara terminé ! Pris {} millisecondes"
  stopping = "Arrêt du serveur Luminara..."
  stopped = "Le serveur Luminara s'est arrêté"
  crash-report-saved = "Luminara a sauvegardé le rapport de crash du serveur dans {}, veuillez vérifier !"
  crash-report-failed = "Impossible de sauvegarder le rapport de crash du serveur sur le disque, vérifiez l'espace disque et les permissions !"
  unexpected-exception = "Exception inattendue rencontrée"
  overload-warning = "Impossible de suivre ! Le serveur est-il surchargé ? En retard de {} millisecondes !"
  exception-stopping = "Exception lors de l'arrêt du serveur"
  async-world-save-starting = "Début de la sauvegarde asynchrone du monde lors de l'arrêt du serveur..."
  async-world-save-starting-general = "Début de la sauvegarde asynchrone du monde..."
  async-world-save-failed = "Échec de la sauvegarde du monde {}"
  saving-chunks = "Sauvegarde des chunks du monde '{}'"
  minecraft-version = "Démarrage du serveur Minecraft pour la version client {}"
  java-memory-too-low = "Mémoire insuffisante allouée pour le démarrage du serveur, allouez au moins 1024MB ! Référence : \"java -Xmx1024M -Xms1024M -jar server.jar\""
  loading-properties = "Chargement du fichier de configuration du serveur (server.properties)..."
  default-gamemode = "Mode de jeu par défaut : {}"
  starting-server = "Démarrage du serveur Minecraft sur {}:{}"
  player-auth-warning1 = "/// !!! Avertissement Luminara !!! ///"
  player-auth-warning2 = "Votre serveur est en mode hors ligne et n'authentifiera pas les noms des joueurs, assurez la sécurité du serveur !"
  player-auth-warning3 = "Le mode hors ligne permet aux joueurs LAN de rejoindre sans authentification, mais présente aussi des risques de sécurité !"
  player-auth-warning4 = "Pour activer le mode en ligne, définissez \"online-mode\" à \"true\" dans server.properties !"
  bind-port-warning1 = "/// !!! Avertissement Luminara !!! ///"
  bind-port-warning2 = "Situation inattendue survenue : {}"
  bind-port-warning3 = "Le port {} peut déjà être utilisé par un autre serveur !"
  start-done = "Démarrage de Luminara terminé ! Temps total : {}, utilisez /help pour l'aide des commandes !"
}

# Optimization system messages
optimization {
  chunk {
    unloading = "Déchargement du chunk [{}, {}] dans le monde {}"
    unloaded = "{} chunks déchargés"
    rate-limit = "Limite de taux de chargement des chunks : original {} -> limité {}"
  }
  memory {
    cleanup-start = "Début du nettoyage de la mémoire..."
    cleanup-complete = "Nettoyage de la mémoire terminé, {} MB libérés"
    high-usage = "Utilisation mémoire élevée : {}%"
    gc-triggered = "Collecte des déchets déclenchée"
    cache-cleanup-completed = "Nettoyage du cache Luminara terminé"
    cache-cleanup-error = "Erreur lors du nettoyage du cache"
    cache-cleanup-failed = "Échec du nettoyage du cache"
  }
  manager {
    shutdown-error = "Erreur lors de l'arrêt du système d'optimisation"
  }
  async-ai {
    calculation-error = "Erreur lors du calcul IA asynchrone"
    processing-error = "Erreur lors du traitement de l'entité {} en IA asynchrone"
    ai-calculation-error = "Erreur dans les calculs d'IA"
  }
  async-collision {
    calculation-error = "Erreur lors du calcul de collision asynchrone"
    processing-error = "Erreur lors du traitement de l'entité {} en collision asynchrone"
    check-error = "Erreur lors de la vérification de collision"
    handling-error = "Erreur lors de la gestion de collision asynchrone entre {} et {}"
    calculation-general-error = "Erreur dans les calculs de collision"
  }
  async-event {
    disabled-due-to-errors = "Traitement asynchrone désactivé pour le type d'événement en raison d'erreurs : {}"
    handler-error = "Erreur dans le gestionnaire d'événements asynchrone pour l'événement {}"
    registered = "Événement asynchrone enregistré : {}"
    interrupted-shutdown = "Interrompu pendant l'arrêt"
    registered-sync = "Événement synchrone enregistré : {}"
  }
}

# Velocity forwarding messages
velocity {
  enabled = "Transfert Velocity Modern activé"
  disabled = "Transfert Velocity Modern désactivé"
  loaded-argument-types = "{} types d'arguments d'intégration chargés"
  failed-load-argument-types = "Échec du chargement des types d'arguments d'intégration, utilisation des valeurs par défaut"
}

# Error messages
error {
  class-not-found = "Classe non trouvée : {}"
  method-not-found = "Méthode non trouvée : {}"
  field-not-found = "Champ non trouvé : {}"
  invalid-configuration = "Fichier de configuration invalide : {}"
  file-not-found = "Fichier non trouvé : {}"
  permission-denied = "Permission refusée : {}"
  network-error = "Erreur réseau : {}"
  database-error = "Erreur de base de données : {}"
  plugin-error = "Erreur de plugin : {}"
  mixin-error = "Erreur Mixin : {}"
}

# Warning messages
warning {
  deprecated-api = "Utilisation d'une API dépréciée : {}"
  performance-issue = "Problème de performance détecté : {}"
  memory-low = "Avertissement de mémoire faible, utilisation actuelle : {}%"
  disk-space-low = "Espace disque faible : {} MB restants"
  plugin-conflict = "Conflit de plugin détecté : {} peut entrer en conflit avec {}"
  async-operation = "Avertissement d'opération asynchrone : {}"
}

# World management messages
world {
  creating = "Création du monde {}"
  created = "Monde {} créé"
  loading = "Chargement du monde {}"
  loaded = "Monde {} chargé"
  unloading = "Déchargement du monde {}"
  unloaded = "Monde {} déchargé"
  saving = "Sauvegarde du monde : {}"
  saved-successfully = "Monde {} sauvegardé avec succès"
  save-error = "Erreur lors de la sauvegarde du monde {}"
}

# Mod integration messages
mod {
  conflict-detected = "Conflit de mod détecté : {} vs {}"
}

# Sign block entity messages
sign {
  non-editable-warning = "Le joueur {} a essayé de modifier un panneau non-éditable"
}

# Enum extender messages
enum {
  not-found-warning = "Attendu de trouver {} dans {}, mais non trouvé"
}

# World symlink messages
symlink {
  create-error = "Erreur lors de la création du lien symbolique"
}

# Distribution validation messages
dist {
  logic-world-check = "Classe de niveau {} considérée comme monde logique : {}"
}

# Chat system messages
chat {
  message-too-long = "Message de chat trop long !"
  empty-message-warning = "{} a essayé d'envoyer un message vide"
  long-message-warning = "{} a essayé d'envoyer un message trop long : {} caractères"
  illegal-characters = "Le message de chat contient des caractères illégaux"
  player-removed = "Le joueur a été supprimé, impossible d'envoyer le message"
}

# Player action messages
player {
  dropped-items-quickly = "{} a lâché des objets trop rapidement !"
  dropped-items-disconnect = "Vous avez lâché des objets trop rapidement (triche ?)"
  invalid-hotbar = "{} a essayé de définir une sélection de barre d'action invalide"
  invalid-hotbar-disconnect = "Sélection de barre d'action invalide (triche ?)"
  command-issued = "{} a exécuté la commande serveur : {}"
  internal-command-error = "Erreur interne lors de l'exécution de cette commande"
  book-edited-quickly = "Livre édité trop rapidement !"
}

comments {
  _v.comment = [
    "Dépôt: https://github.com/QianMoo0121/Luminara"
    "Problèmes: https://github.com/QianMoo0121/Luminara/issues"
    ""
    ""
    "Numéro de version de la configuration, ne pas modifier."
  ]
  locale.comment = "Langage/paramètres I18n"
  optimization {
    comment = "Paramètres d'optimisation"
    goal-selector-update-interval.comment = [
      "Intervalle en ticks pour mettre à jour le sélecteur d'objectifs"
      "Des valeurs plus élevées coûtent moins de ressources"
      "Fait que les mobs changent leurs objectifs moins souvent"
    ]
  }
  async-catcher.comment = [
    "Paramètres Async Catcher"
    "Il existe quatre modes, le mode BLOCK est recommandé !"
    "NONE - Ne fait rien"
    "DISPATCH - Ne bloque rien et affiche les informations dans la console"
    "BLOCK - Continue l'execution dans le thread principal"
    "EXCEPTION - Affiche une erreur dans la console"
  ]
  async-catcher.dump.comment = "Affiche les informations contenues dans le fichier debug.log"
  async-world-save.comment = [
    "Paramètres liés à la sauvegarde asynchrone du monde"
    "Sauvegarder les données du monde de manière asynchrone lors de l'arrêt du serveur pour réduire le temps d'arrêt"
  ]
  async-world-save.enabled.comment = "Activer ou non la fonction de sauvegarde asynchrone du monde"
  async-world-save.timeout-seconds.comment = [
    "Délai d'attente de la sauvegarde asynchrone en secondes"
    "Si la sauvegarde ne se termine pas dans ce délai, le serveur continuera le processus d'arrêt"
  ]
  async-world-save.save-world-data.comment = "Inclure ou non les données du monde dans la sauvegarde asynchrone"
  compatibility {
    symlink-world.comment = [
      "Créer des liens symboliques vers le dossier de dimension de mod qui correspond au nom du monde Bukkit"
      "Activer ceci pourrait améliorer la compatibilité des plugins"
      "Changer ceci sur un serveur de production causera des changements aux noms des mondes de mod"
      "  et causera une perte de données sur les plugins dépendant des noms de monde"
      "Voir https://github.com/IzzelAliz/Arclight/wiki/World-Symlink pour plus de détails"
    ]
    extra-logic-worlds.comment = [
      "Logique des mondes supplémentaires en cours d'exécution"
      "Si des mods ne fonctionnent pas bien, essayez de rechercher les noms de classe dans les logs liés à [EXT_LOGIC] et ajoutez-les ici"
    ]
    forward-permission.comment = [
      "true - Transférer les requêtes de permission Forge vers Bukkit"
      "false - Désactiver le transfert de permission"
      "reverse - Transférer les requêtes de permission de joueur Bukkit vers Forge"
    ]
    valid-username-regex.comment = [
      "Regex pour la vérification de nom d'utilisateur valide. Laissez vide pour utiliser la vérification vanilla"
      "Ce qui suit permet les caractères chinois:"
      "valid-username-regex = \"^[ -~\\\\p{sc=Han}]{1,16}$\""
      "Ce qui suit permet à tout nom d'utilisateur de se connecter:"
      "valid-username-regex = \".+\""
    ]
    lenient-item-tag-match.comment = [
      "Permet aux objets avec une étiquette nbt vide de s'empiler sur des objets sans étiquette"
    ]
  }
  velocity {
    comment = "Paramètres liés à Velocity Modern Forwarding"
    enabled.comment = [
      "Activer ou non le support de Velocity Modern Forwarding"
      "Lorsqu'activé, permet l'intégration avec le serveur proxy Velocity"
    ]
    online-mode.comment = [
      "Activer ou non la vérification du mode en ligne"
      "Devrait généralement correspondre au paramètre online-mode dans la configuration Velocity"
    ]
    forwarding-secret.comment = [
      "Clé secrète de transfert Velocity"
      "Doit correspondre exactement au forwarding-secret dans le fichier de configuration Velocity"
      "Utilisé pour authentifier les demandes de connexion de Velocity"
    ]
    debug-logging.comment = [
      "Afficher ou non les informations de débogage liées au transfert Velocity dans les logs"
      "Activez ceci pour aider à diagnostiquer les problèmes de connexion"
    ]
  }
}

#Translation by Spark
